# KiotViet MCP Server - Development Guide

## Overview

This document provides comprehensive guidance for developing and extending the KiotViet MCP Server.

## Project Structure

```
src/
├── config/                    # Configuration management
│   ├── environment.ts         # Environment variable validation with Zod
│   ├── kiotviet-config.ts     # KiotViet-specific configuration
│   └── index.ts               # Configuration exports
├── core/
│   ├── api/
│   │   └── kiotviet-client.ts # KiotViet API client with OAuth2 auth
│   ├── tools/
│   │   └── kiotviet-get-categories.ts # Example KiotViet tool
│   ├── types/
│   │   └── kiotviet.ts        # KiotViet API types and Zod schemas
│   ├── utils/
│   │   └── logger.ts          # Structured logging utility
│   ├── tools.ts               # Tool registration
│   ├── resources.ts           # Resource registration
│   └── prompts.ts             # Prompt registration
├── server/
│   ├── server.ts              # Main MCP server setup
│   ├── http-server.ts         # HTTP transport server
│   └── index.ts               # Server exports
└── index.ts                   # Application entry point
```

## KiotViet API Integration

### Authentication Flow

The KiotViet API uses OAuth2 client credentials flow:

1. **Token Request**: POST to `https://id.kiotviet.vn/connect/token`
2. **Required Parameters**:
   - `scopes=PublicApi.Access`
   - `grant_type=client_credentials`
   - `client_id=<your_client_id>`
   - `client_secret=<your_client_secret>`
3. **Token Response**: Contains `access_token`, `expires_in`, `token_type`, `scope`
4. **API Requests**: Include `Authorization: Bearer <token>` and `Retailer: <retailer_name>` headers

### API Client Architecture

The `KiotVietApiClient` class handles:

- **Automatic Token Management**: Requests and refreshes tokens as needed
- **Request Authentication**: Adds required headers to all API requests
- **Error Handling**: Comprehensive error handling with proper logging
- **Timeout Management**: Configurable request timeouts
- **Health Checks**: Built-in health check functionality

### Example API Client Usage

```typescript
import { KiotVietApiClient } from '../core/api/kiotviet-client.js';
import { createKiotVietConfig } from '../config/kiotviet-config.js';

// Initialize client
const config = createKiotVietConfig();
const client = new KiotVietApiClient(config);

// Get categories
const response = await client.getCategories({
  orderDirection: 'Asc',
  hierachicalData: false,
  pageSize: 20
});
```

## Creating New KiotViet Tools

### 1. Define Types

First, define the types for your new API endpoint in `src/core/types/kiotviet.ts`:

```typescript
// Add new interface for your data type
export interface KiotVietProduct {
  id: number;
  name: string;
  price: number;
  // ... other fields
}

// Add request parameters interface
export interface KiotVietProductsRequest {
  pageSize?: number;
  categoryId?: number;
  // ... other parameters
}

// Add Zod schemas for validation
export const KiotVietProductSchema = z.object({
  id: z.number(),
  name: z.string(),
  price: z.number(),
  // ... other fields
});
```

### 2. Extend API Client

Add the new method to `KiotVietApiClient`:

```typescript
// In src/core/api/kiotviet-client.ts
async getProducts(request?: KiotVietProductsRequest): Promise<KiotVietApiResponse<KiotVietProduct>> {
  this.logger.info('Getting products from KiotViet', { request });

  const response = await this.makeApiRequest<KiotVietApiResponse<KiotVietProduct>>(
    '/products',
    {
      method: 'GET',
      body: request,
    }
  );

  return response;
}
```

### 3. Create Tool Class

Create a new tool file (e.g., `src/core/tools/kiotviet-get-products.ts`):

```typescript
import { z } from 'zod';
import { KiotVietApiClient } from '../api/kiotviet-client.js';
import { createKiotVietConfig } from '../../config/kiotviet-config.js';
import { Logger } from '../utils/logger.js';

// Define parameter schema
const GetProductsParametersSchema = z.object({
  pageSize: z.number().min(1).max(100).optional().default(20),
  categoryId: z.number().optional(),
});

export class KiotVietGetProductsTool {
  private logger: Logger;
  private client: KiotVietApiClient;
  private initialized = false;

  constructor() {
    this.logger = Logger.createContextLogger('KiotVietGetProductsTool');
  }

  async initialize(): Promise<void> {
    if (this.initialized) return;
    
    const config = createKiotVietConfig();
    this.client = new KiotVietApiClient(config);
    
    const isHealthy = await this.client.healthCheck();
    if (!isHealthy) {
      throw new Error('Failed to connect to KiotViet API');
    }
    
    this.initialized = true;
  }

  async execute(parameters: any): Promise<any> {
    if (!this.initialized) {
      await this.initialize();
    }

    const validatedParams = GetProductsParametersSchema.parse(parameters);
    const response = await this.client.getProducts(validatedParams);
    
    return {
      success: true,
      data: response,
    };
  }

  getToolMetadata() {
    return {
      name: 'kiotviet_get_products',
      description: 'Get products from KiotViet',
      parameters: GetProductsParametersSchema,
    };
  }
}
```

### 4. Register Tool

Add the tool to `src/core/tools.ts`:

```typescript
import { KiotVietGetProductsTool } from "./tools/kiotviet-get-products.js";

export function registerTools(server: FastMCP) {
  // ... existing tools

  // New products tool
  const getProductsToolInstance = new KiotVietGetProductsTool();
  
  server.addTool({
    name: "kiotviet_get_products",
    description: "Get products from KiotViet",
    parameters: GetProductsParametersSchema,
    execute: async (params) => {
      const result = await getProductsToolInstance.execute(params);
      if (!result.success) {
        throw new Error(result.error?.message || 'Tool execution failed');
      }
      return result.data;
    }
  });
}
```

## Configuration Management

### Environment Variables

All configuration is managed through environment variables with Zod validation:

```typescript
// In src/config/environment.ts
const EnvironmentSchema = z.object({
  KIOTVIET_RETAILER: z.string().min(1),
  KIOTVIET_CLIENT_ID: z.string().min(1),
  KIOTVIET_CLIENT_SECRET: z.string().min(1),
  // ... other variables
});
```

### Configuration Access

```typescript
import { getEnvironmentConfig } from '../config/environment.js';

const config = getEnvironmentConfig();
console.log(config.kiotviet.retailer);
```

## Logging

### Structured Logging

The project uses structured logging with context:

```typescript
import { Logger } from '../utils/logger.js';

const logger = Logger.createContextLogger('MyComponent');

logger.info('Operation started', { userId: 123 });
logger.error('Operation failed', error, { context: 'additional info' });
```

### Log Levels

- `error`: Error conditions
- `warn`: Warning conditions
- `info`: Informational messages
- `debug`: Debug-level messages

## Testing

### Connection Testing

Test your KiotViet API connection:

```bash
bun run test-kiotviet
```

### Manual Testing

```typescript
// Create a test script
import { KiotVietGetCategoriesTool } from '../src/core/tools/kiotviet-get-categories.js';

const tool = new KiotVietGetCategoriesTool();
await tool.initialize();

const result = await tool.execute({
  pageSize: 5,
  orderDirection: 'Asc'
});

console.log(result);
```

## Error Handling

### API Errors

The API client automatically handles:

- Authentication failures (token refresh)
- Network timeouts
- Rate limiting
- HTTP error status codes

### Tool Errors

Tools should return structured error responses:

```typescript
return {
  success: false,
  error: {
    message: 'Descriptive error message',
    code: 'ERROR_CODE',
    details: { /* additional context */ }
  }
};
```

## Best Practices

### 1. Type Safety

- Always define TypeScript interfaces for API responses
- Use Zod schemas for runtime validation
- Validate all input parameters

### 2. Error Handling

- Use structured error responses
- Log errors with context
- Handle authentication failures gracefully

### 3. Performance

- Implement appropriate caching where beneficial
- Use pagination for large datasets
- Set reasonable timeouts

### 4. Security

- Never log sensitive information (tokens, secrets)
- Validate all inputs
- Use environment variables for configuration

### 5. Maintainability

- Use consistent naming conventions
- Add comprehensive logging
- Write clear documentation
- Follow the established patterns

## Deployment

### Environment Setup

1. Copy `.env.example` to `.env`
2. Configure KiotViet credentials
3. Set appropriate log levels for production

### Production Considerations

- Set `NODE_ENV=production`
- Use appropriate log levels (`info` or `warn`)
- Configure proper timeout values
- Monitor API rate limits
- Implement health checks
