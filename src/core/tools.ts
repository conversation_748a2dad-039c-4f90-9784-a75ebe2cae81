import { FastMCP } from "fastmcp";
import { z } from "zod";
import * as services from "./services/index.js";
import { KiotVietGetCategoriesTool, GetCategoriesParametersSchema } from "./tools/kiotviet-get-categories.js";
import { Logger } from "./utils/logger.js";

const logger = Logger.createContextLogger('ToolsRegistry');

/**
 * Register all tools with the MCP server
 *
 * @param server The FastMCP server instance
 */
export function registerTools(server: FastMCP) {
  // KiotViet Get Categories tool
  const getCategoriesToolInstance = new KiotVietGetCategoriesTool();

  server.addTool({
    name: "kiotviet_get_categories",
    description: "Get product categories from KiotViet retail system with optional filtering and pagination",
    parameters: GetCategoriesParametersSchema,
    execute: async (params) => {
      logger.info('Executing kiotviet_get_categories tool', { params });

      try {
        const result = await getCategoriesToolInstance.execute(params);

        if (!result.success) {
          logger.error('KiotViet Get Categories tool execution failed', undefined, {
            error: result.error,
            params,
          });
          throw new Error(result.error?.message || 'Tool execution failed');
        }

        logger.info('KiotViet Get Categories tool executed successfully', {
          totalCategories: result.metadata?.totalCategories,
          executionTime: result.metadata?.executionTime,
        });

        return result.data;
      } catch (error) {
        logger.error('Error executing KiotViet Get Categories tool', error as Error, { params });
        throw error;
      }
    }
  });

  // Keep original greeting tools for backward compatibility
  server.addTool({
    name: "hello_world",
    description: "A simple hello world tool",
    parameters: z.object({
      name: z.string().describe("Name to greet")
    }),
    execute: async (params) => {
      const greeting = services.GreetingService.generateGreeting(params.name);
      return greeting;
    }
  });

  server.addTool({
    name: "goodbye",
    description: "A simple goodbye tool",
    parameters: z.object({
      name: z.string().describe("Name to bid farewell to")
    }),
    execute: async (params) => {
      const farewell = services.GreetingService.generateFarewell(params.name);
      return farewell;
    }
  });

  logger.info('All tools registered successfully');
}