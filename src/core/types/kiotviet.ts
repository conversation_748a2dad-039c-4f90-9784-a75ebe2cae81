import { z } from 'zod';

// KiotViet API Authentication
export interface KiotVietAuth {
  retailer: string;
  clientId: string;
  clientSecret: string;
  accessToken?: string;
  tokenExpiry?: Date;
}

// KiotViet API Configuration
export interface KiotVietConfig {
  authUrl: string;
  apiUrl: string;
  auth: KiotVietAuth;
  timeout: number;
  retries: number;
  rateLimit: {
    requests: number;
    window: number;
  };
}

// KiotViet Token Response
export interface KiotVietTokenResponse {
  access_token: string;
  expires_in: number;
  token_type: string;
  scope: string;
}

// KiotViet API Response Structure
export interface KiotVietApiResponse<T = any> {
  total: number;
  pageSize: number;
  data: T[];
  timestamp: string;
}

// KiotViet Category
export interface KiotVietCategory {
  categoryId: number;
  categoryName: string;
  retailerId: number;
  modifiedDate: string;
  createdDate: string;
  rank: number;
  parentId?: number;
  hasChild?: boolean;
  isActive?: boolean;
}

// KiotViet Categories Request
export interface KiotVietCategoriesRequest {
  orderDirection?: 'Asc' | 'Desc';
  hierachicalData?: boolean;
  pageSize?: number;
  currentItem?: number;
}

// Validation Schemas
export const KiotVietAuthSchema = z.object({
  retailer: z.string().min(1),
  clientId: z.string().min(1),
  clientSecret: z.string().min(1),
  accessToken: z.string().optional(),
  tokenExpiry: z.date().optional(),
});

export const KiotVietConfigSchema = z.object({
  authUrl: z.string().url(),
  apiUrl: z.string().url(),
  auth: KiotVietAuthSchema,
  timeout: z.number().min(1000),
  retries: z.number().min(0).max(5),
  rateLimit: z.object({
    requests: z.number().min(1),
    window: z.number().min(1000),
  }),
});

export const KiotVietCategorySchema = z.object({
  categoryId: z.number(),
  categoryName: z.string(),
  retailerId: z.number(),
  modifiedDate: z.string(),
  createdDate: z.string(),
  rank: z.number(),
  parentId: z.number().optional(),
  hasChild: z.boolean().optional(),
  isActive: z.boolean().optional(),
});

export const KiotVietCategoriesRequestSchema = z.object({
  orderDirection: z.enum(['Asc', 'Desc']).optional(),
  hierachicalData: z.boolean().optional(),
  pageSize: z.number().min(1).max(100).optional(),
  currentItem: z.number().min(0).optional(),
});

export const KiotVietApiResponseSchema = z.object({
  total: z.number(),
  pageSize: z.number(),
  data: z.array(z.any()),
  timestamp: z.string(),
});
