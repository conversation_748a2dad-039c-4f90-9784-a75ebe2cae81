import { 
  KiotVietConfig, 
  KiotVietTokenResponse, 
  KiotVietApiResponse,
  KiotVietCategory,
  KiotVietCategoriesRequest 
} from '../types/kiotviet.js';
import { Logger } from '../utils/logger.js';

export class KiotVietApiClient {
  private logger: Logger;
  private config: KiotVietConfig;
  private accessToken?: string;
  private tokenExpiry?: Date;

  constructor(config: KiotVietConfig) {
    this.config = config;
    this.logger = Logger.createContextLogger('KiotVietApiClient');
  }

  // Get access token, refresh if needed
  async getAccessToken(): Promise<string> {
    if (this.accessToken && this.isTokenValid()) {
      return this.accessToken;
    }

    await this.requestNewToken();
    return this.accessToken!;
  }

  // Check if current token is valid (not expired)
  private isTokenValid(): boolean {
    if (!this.accessToken || !this.tokenExpiry) {
      return false;
    }

    // Add 5 minute buffer before expiry
    const bufferTime = 5 * 60 * 1000; // 5 minutes in milliseconds
    return this.tokenExpiry.getTime() > Date.now() + bufferTime;
  }

  // Request a new access token using client credentials
  private async requestNewToken(): Promise<void> {
    this.logger.info('Requesting new access token from KiotViet');

    const tokenUrl = `${this.config.authUrl}/connect/token`;
    const body = new URLSearchParams({
      scopes: 'PublicApi.Access',
      grant_type: 'client_credentials',
      client_id: this.config.auth.clientId,
      client_secret: this.config.auth.clientSecret,
    });

    try {
      const response = await fetch(tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: body.toString(),
        signal: AbortSignal.timeout(this.config.timeout),
      });

      if (!response.ok) {
        const errorText = await response.text();
        this.logger.error('Token request failed', undefined, {
          status: response.status,
          statusText: response.statusText,
          error: errorText,
        });
        throw new Error(`Authentication failed: ${response.status} ${response.statusText}`);
      }

      const tokenData: KiotVietTokenResponse = await response.json();
      this.updateAuthTokens(tokenData);

      this.logger.info('Successfully obtained new access token', {
        expiresIn: tokenData.expires_in,
        tokenType: tokenData.token_type,
        scope: tokenData.scope,
      });
    } catch (error) {
      this.logger.error('Failed to request new token', error as Error);
      throw new Error(`Authentication failed: ${(error as Error).message}`);
    }
  }

  // Update auth tokens
  private updateAuthTokens(tokenData: KiotVietTokenResponse): void {
    this.accessToken = tokenData.access_token;
    
    // Calculate expiry time (subtract 5 minutes for safety)
    const expiryTime = new Date(Date.now() + (tokenData.expires_in - 300) * 1000);
    this.tokenExpiry = expiryTime;
  }

  // Make authenticated API request
  private async makeApiRequest<T>(
    endpoint: string,
    options: {
      method?: string;
      body?: any;
      headers?: Record<string, string>;
    } = {}
  ): Promise<T> {
    const { method = 'GET', body, headers = {} } = options;
    
    // Get access token
    const token = await this.getAccessToken();
    
    // Build request URL
    const url = `${this.config.apiUrl}${endpoint}`;
    
    // Prepare headers
    const requestHeaders: Record<string, string> = {
      'Retailer': this.config.auth.retailer,
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
      ...headers,
    };

    this.logger.debug('Making API request', {
      method,
      url,
      hasBody: !!body,
    });

    try {
      const response = await fetch(url, {
        method,
        headers: requestHeaders,
        body: body ? JSON.stringify(body) : undefined,
        signal: AbortSignal.timeout(this.config.timeout),
      });

      if (!response.ok) {
        const errorText = await response.text();
        this.logger.error('API request failed', undefined, {
          method,
          url,
          status: response.status,
          statusText: response.statusText,
          error: errorText,
        });
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      this.logger.debug('API request successful', {
        method,
        url,
        status: response.status,
      });

      return data;
    } catch (error) {
      this.logger.error('API request error', error as Error, {
        method,
        url,
      });
      throw error;
    }
  }

  // Get categories from KiotViet API
  async getCategories(request?: KiotVietCategoriesRequest): Promise<KiotVietApiResponse<KiotVietCategory>> {
    this.logger.info('Getting categories from KiotViet', { request });

    const response = await this.makeApiRequest<KiotVietApiResponse<KiotVietCategory>>(
      '/categories',
      {
        method: 'GET',
        body: request,
      }
    );

    this.logger.info('Successfully retrieved categories', {
      total: response.total,
      count: response.data.length,
    });

    return response;
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      // Try to get a small number of categories as a health check
      await this.getCategories({ pageSize: 1 });
      return true;
    } catch (error) {
      this.logger.warn('Health check failed', { error: (error as Error).message });
      return false;
    }
  }

  // Get authentication status
  getAuthStatus() {
    return {
      hasToken: !!this.accessToken,
      isValid: this.isTokenValid(),
      expiresAt: this.tokenExpiry,
      expiresIn: this.tokenExpiry 
        ? Math.max(0, this.tokenExpiry.getTime() - Date.now())
        : undefined,
    };
  }
}
