import { z } from 'zod';
import { KiotVietApiClient } from '../api/kiotviet-client.js';
import { createKiotVietConfig } from '../../config/kiotviet-config.js';
import { KiotVietCategoriesRequestSchema } from '../types/kiotviet.js';
import { Logger } from '../utils/logger.js';

// Parameter schema for the get categories tool
const GetCategoriesParametersSchema = z.object({
  orderDirection: z.enum(['Asc', 'Desc']).optional().default('Asc').describe('Sort direction for categories'),
  hierachicalData: z.boolean().optional().default(false).describe('Whether to return hierarchical data structure'),
  pageSize: z.number().min(1).max(100).optional().default(20).describe('Number of categories to return per page'),
  currentItem: z.number().min(0).optional().default(0).describe('Starting item index for pagination'),
});

export type GetCategoriesParameters = z.infer<typeof GetCategoriesParametersSchema>;

export class KiotVietGetCategoriesTool {
  private logger: Logger;
  private client: KiotVietApiClient;
  private initialized = false;

  constructor() {
    this.logger = Logger.createContextLogger('KiotVietGetCategoriesTool');
  }

  // Initialize the tool and API client
  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      this.logger.info('Initializing KiotViet Get Categories tool');

      // Create KiotViet API client
      const config = createKiotVietConfig();
      this.client = new KiotVietApiClient(config);

      // Test connection
      const isHealthy = await this.client.healthCheck();
      if (!isHealthy) {
        throw new Error('Failed to connect to KiotViet API during initialization');
      }

      this.initialized = true;
      this.logger.info('KiotViet Get Categories tool initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize KiotViet Get Categories tool', error as Error);
      throw new Error(`Initialization failed: ${(error as Error).message}`);
    }
  }

  // Validate parameters using the tool's schema
  validateParameters(parameters: any): { valid: boolean; errors?: string[] } {
    try {
      GetCategoriesParametersSchema.parse(parameters);
      return { valid: true };
    } catch (error) {
      if (error instanceof z.ZodError) {
        return {
          valid: false,
          errors: error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
        };
      }
      return {
        valid: false,
        errors: ['Unknown validation error']
      };
    }
  }

  // Execute the tool
  async execute(parameters: any): Promise<{
    success: boolean;
    data?: any;
    error?: {
      message: string;
      code?: string;
      details?: any;
    };
    metadata?: {
      executionTime?: number;
      apiCalls?: number;
      totalCategories?: number;
      [key: string]: any;
    };
  }> {
    const startTime = Date.now();
    
    try {
      // Ensure tool is initialized
      if (!this.initialized) {
        await this.initialize();
      }

      // Validate parameters
      const validation = this.validateParameters(parameters);
      if (!validation.valid) {
        return {
          success: false,
          error: {
            message: 'Invalid parameters provided',
            code: 'VALIDATION_ERROR',
            details: validation.errors,
          },
          metadata: {
            executionTime: Date.now() - startTime,
          },
        };
      }

      const validatedParams = GetCategoriesParametersSchema.parse(parameters);

      this.logger.info('Executing KiotViet Get Categories', {
        parameters: validatedParams,
      });

      // Prepare request for KiotViet API
      const request = {
        orderDirection: validatedParams.orderDirection,
        hierachicalData: validatedParams.hierachicalData,
        pageSize: validatedParams.pageSize,
        currentItem: validatedParams.currentItem,
      };

      // Call KiotViet API
      const response = await this.client.getCategories(request);
      
      const executionTime = Date.now() - startTime;
      
      this.logger.info('Successfully retrieved categories from KiotViet', {
        totalCategories: response.total,
        returnedCategories: response.data.length,
        executionTime,
      });

      // Format response for MCP client
      const formattedResponse = {
        categories: response.data,
        pagination: {
          total: response.total,
          pageSize: response.pageSize,
          currentItem: validatedParams.currentItem,
          hasMore: (validatedParams.currentItem + response.data.length) < response.total,
        },
        metadata: {
          timestamp: response.timestamp,
          orderDirection: validatedParams.orderDirection,
          hierachicalData: validatedParams.hierachicalData,
        },
      };

      return {
        success: true,
        data: {
          content: [
            {
              type: "text",
              text: JSON.stringify(formattedResponse, null, 2)
            }
          ]
        },
        metadata: {
          executionTime,
          apiCalls: 1,
          totalCategories: response.total,
          returnedCategories: response.data.length,
          toolName: 'kiotviet_get_categories',
        },
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      this.logger.error('Failed to get categories from KiotViet', error as Error, {
        parameters,
        executionTime,
      });

      return {
        success: false,
        error: {
          message: `Failed to get categories: ${(error as Error).message}`,
          code: 'API_ERROR',
          details: {
            originalError: (error as Error).message,
          },
        },
        metadata: {
          executionTime,
          toolName: 'kiotviet_get_categories',
        },
      };
    }
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      if (!this.initialized) {
        await this.initialize();
      }
      return await this.client.healthCheck();
    } catch (error) {
      this.logger.warn('Health check failed', { error: (error as Error).message });
      return false;
    }
  }

  // Get tool metadata for MCP registration
  getToolMetadata() {
    return {
      name: 'kiotviet_get_categories',
      description: 'Get product categories from KiotViet retail system with optional filtering and pagination',
      parameters: GetCategoriesParametersSchema,
    };
  }
}

// Export the parameter schema for external use
export { GetCategoriesParametersSchema };
