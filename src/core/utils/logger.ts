// Structured logging utility
export interface LogEntry {
  level: 'error' | 'warn' | 'info' | 'debug';
  message: string;
  timestamp: string;
  context?: string;
  metadata?: Record<string, any>;
  error?: {
    message: string;
    stack?: string;
    code?: string;
  };
}

export class Logger {
  private static instance: Logger;
  private logLevel: 'error' | 'warn' | 'info' | 'debug' = 'info';
  private context?: string;

  private constructor(context?: string) {
    this.context = context;
    this.logLevel = (process.env.LOG_LEVEL as any) || 'info';
  }

  static getInstance(context?: string): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger(context);
    }
    return Logger.instance;
  }

  static createContextLogger(context: string): Logger {
    return new Logger(context);
  }

  private shouldLog(level: string): boolean {
    const levels = ['error', 'warn', 'info', 'debug'];
    return levels.indexOf(level) <= levels.indexOf(this.logLevel);
  }

  private formatLog(entry: LogEntry): string {
    if (process.env.NODE_ENV === 'development') {
      return `[${entry.timestamp}] ${entry.level.toUpperCase()} ${entry.context ? `[${entry.context}]` : ''} ${entry.message}${entry.metadata ? ` ${JSON.stringify(entry.metadata)}` : ''}`;
    }
    return JSON.stringify(entry);
  }

  private log(level: LogEntry['level'], message: string, metadata?: Record<string, any>, error?: Error): void {
    if (!this.shouldLog(level)) return;

    const entry: LogEntry = {
      level,
      message,
      timestamp: new Date().toISOString(),
      context: this.context,
      metadata,
    };

    if (error) {
      entry.error = {
        message: error.message,
        stack: error.stack,
        code: (error as any).code,
      };
    }

    const output = this.formatLog(entry);
    
    if (level === 'error') {
      console.error(output);
    } else if (level === 'warn') {
      console.warn(output);
    } else {
      console.log(output);
    }
  }

  error(message: string, error?: Error, metadata?: Record<string, any>): void {
    this.log('error', message, metadata, error);
  }

  warn(message: string, metadata?: Record<string, any>): void {
    this.log('warn', message, metadata);
  }

  info(message: string, metadata?: Record<string, any>): void {
    this.log('info', message, metadata);
  }

  debug(message: string, metadata?: Record<string, any>): void {
    this.log('debug', message, metadata);
  }

  child(context: string): Logger {
    return Logger.createContextLogger(`${this.context || ''}:${context}`);
  }
}
