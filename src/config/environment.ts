import { z } from 'zod';

// Environment configuration schema
const EnvironmentSchema = z.object({
  // Application
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.coerce.number().min(1).max(65535).default(3001),
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  
  // MCP Server
  MCP_SERVER_NAME: z.string().default('KiotViet MCP Server'),
  MCP_SERVER_VERSION: z.string().default('1.0.0'),
  
  // Health Check
  ENABLE_HEALTH_CHECK: z.coerce.boolean().default(true),
  HEALTH_CHECK_PORT: z.coerce.number().min(1).max(65535).optional(),
  
  // KiotViet API Configuration
  KIOTVIET_AUTH_URL: z.string().url().default('https://id.kiotviet.vn'),
  KIOTVIET_API_URL: z.string().url().default('https://public.kiotapi.com'),
  KIOTVIET_RETAILER: z.string().min(1),
  KIOTVIET_CLIENT_ID: z.string().min(1),
  KIOTVIET_CLIENT_SECRET: z.string().min(1),
  
  // API Configuration
  KIOTVIET_TIMEOUT: z.coerce.number().min(1000).default(30000),
  KIOTVIET_RETRIES: z.coerce.number().min(0).max(5).default(3),
  KIOTVIET_RATE_LIMIT_REQUESTS: z.coerce.number().min(1).default(100),
  KIOTVIET_RATE_LIMIT_WINDOW: z.coerce.number().min(1000).default(60000),
  
  // Cache Configuration
  ENABLE_CACHE: z.coerce.boolean().default(true),
  CACHE_TTL: z.coerce.number().min(0).default(300000), // 5 minutes
});

export type Environment = z.infer<typeof EnvironmentSchema>;

// Parse and validate environment variables
export const env = EnvironmentSchema.parse(process.env);

// Environment validation function
export function validateEnvironment(): { valid: boolean; errors?: string[] } {
  try {
    EnvironmentSchema.parse(process.env);
    return { valid: true };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        valid: false,
        errors: error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
      };
    }
    return {
      valid: false,
      errors: ['Unknown validation error']
    };
  }
}

// Get environment-specific configuration
export function getEnvironmentConfig() {
  return {
    isDevelopment: env.NODE_ENV === 'development',
    isProduction: env.NODE_ENV === 'production',
    isTest: env.NODE_ENV === 'test',
    
    server: {
      port: env.PORT,
      name: env.MCP_SERVER_NAME,
      version: env.MCP_SERVER_VERSION,
      enableHealthCheck: env.ENABLE_HEALTH_CHECK,
      healthCheckPort: env.HEALTH_CHECK_PORT,
    },
    
    logging: {
      level: env.LOG_LEVEL,
    },
    
    kiotviet: {
      authUrl: env.KIOTVIET_AUTH_URL,
      apiUrl: env.KIOTVIET_API_URL,
      retailer: env.KIOTVIET_RETAILER,
      clientId: env.KIOTVIET_CLIENT_ID,
      clientSecret: env.KIOTVIET_CLIENT_SECRET,
      timeout: env.KIOTVIET_TIMEOUT,
      retries: env.KIOTVIET_RETRIES,
      rateLimit: {
        requests: env.KIOTVIET_RATE_LIMIT_REQUESTS,
        window: env.KIOTVIET_RATE_LIMIT_WINDOW,
      },
    },
    
    cache: {
      enabled: env.ENABLE_CACHE,
      ttl: env.CACHE_TTL,
    },
  };
}
