import { KiotVietConfig, KiotVietConfigSchema } from '../core/types/kiotviet.js';
import { getEnvironmentConfig } from './environment.js';

// KiotViet API Endpoints
export const KIOTVIET_ENDPOINTS = {
  // Authentication
  AUTH: {
    TOKEN: '/connect/token',
  },
  
  // Categories
  CATEGORIES: {
    LIST: '/categories',
  },
  
  // Products & Inventory
  PRODUCTS: {
    LIST: '/products',
    DETAIL: '/products/{id}',
    SEARCH: '/products/search',
    CREATE: '/products',
    UPDATE: '/products/{id}',
    DELETE: '/products/{id}',
  },
  
  CATEGORIES_OLD: {
    LIST: '/categories',
    DETAIL: '/categories/{id}',
    CREATE: '/categories',
    UPDATE: '/categories/{id}',
  },
  
  INVENTORY: {
    LIST: '/products/{productId}/inventories',
    UPDATE: '/products/{productId}/inventories',
    BRANCHES: '/branches/{branchId}/inventories',
  },
  
  // Customers
  CUSTOMERS: {
    LIST: '/customers',
    DETAIL: '/customers/{id}',
    SEARCH: '/customers/search',
    CREATE: '/customers',
    UPDATE: '/customers/{id}',
    DELETE: '/customers/{id}',
  },
  
  // Orders & Invoices
  ORDERS: {
    LIST: '/orders',
    DETAIL: '/orders/{id}',
    CREATE: '/orders',
    UPDATE: '/orders/{id}',
    DELETE: '/orders/{id}',
  },
  
  INVOICES: {
    LIST: '/invoices',
    DETAIL: '/invoices/{id}',
    CREATE: '/invoices',
    UPDATE: '/invoices/{id}',
  },
  
  // Branches
  BRANCHES: {
    LIST: '/branches',
    DETAIL: '/branches/{id}',
  },
} as const;

// Create KiotViet configuration from environment
export function createKiotVietConfig(): KiotVietConfig {
  const envConfig = getEnvironmentConfig();
  
  const config: KiotVietConfig = {
    authUrl: envConfig.kiotviet.authUrl,
    apiUrl: envConfig.kiotviet.apiUrl,
    auth: {
      retailer: envConfig.kiotviet.retailer,
      clientId: envConfig.kiotviet.clientId,
      clientSecret: envConfig.kiotviet.clientSecret,
    },
    timeout: envConfig.kiotviet.timeout,
    retries: envConfig.kiotviet.retries,
    rateLimit: envConfig.kiotviet.rateLimit,
  };
  
  // Validate configuration
  const validationResult = KiotVietConfigSchema.safeParse(config);
  if (!validationResult.success) {
    throw new Error(`Invalid KiotViet configuration: ${validationResult.error.message}`);
  }
  
  return config;
}

// KiotViet API Rate Limits (based on their documentation)
export const KIOTVIET_RATE_LIMITS = {
  DEFAULT: {
    requests: 100,
    window: 60000, // 1 minute
  },
  CATEGORIES: {
    requests: 50,
    window: 60000,
  },
  PRODUCTS: {
    requests: 50,
    window: 60000,
  },
  ORDERS: {
    requests: 30,
    window: 60000,
  },
  CUSTOMERS: {
    requests: 50,
    window: 60000,
  },
} as const;

// KiotViet API Error Codes
export const KIOTVIET_ERROR_CODES = {
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  RATE_LIMIT: 429,
  SERVER_ERROR: 500,
  BAD_REQUEST: 400,
} as const;

// Default configuration values
export const KIOTVIET_DEFAULTS = {
  TIMEOUT: 30000,
  RETRIES: 3,
  PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
} as const;

// Validate KiotViet configuration
export function validateKiotVietConfig(config: KiotVietConfig): { valid: boolean; errors?: string[] } {
  const result = KiotVietConfigSchema.safeParse(config);
  
  if (result.success) {
    return { valid: true };
  }
  
  return {
    valid: false,
    errors: result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
  };
}
