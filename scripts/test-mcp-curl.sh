#!/bin/bash

# Test MCP HTTP/SSE endpoint with curl

echo "🧪 Testing MCP HTTP/SSE endpoint..."
echo "=================================="

# Check if server is running
if ! curl -s http://localhost:3002/sse > /dev/null; then
    echo "❌ Server not running on port 3002"
    echo "Please start the server with: PORT=3002 bun run start:http"
    exit 1
fi

echo "✅ Server is running on port 3002"

# Step 1: Connect to SSE endpoint to get session info
echo ""
echo "🔌 Step 1: Connecting to SSE endpoint..."
SSE_RESPONSE=$(timeout 5s curl -s -N http://localhost:3002/sse | head -n 5)

if [ -z "$SSE_RESPONSE" ]; then
    echo "❌ No response from SSE endpoint"
    exit 1
fi

echo "📨 SSE Response:"
echo "$SSE_RESPONSE"

# Extract endpoint from SSE response
ENDPOINT_LINE=$(echo "$SSE_RESPONSE" | grep "event: endpoint" -A 1 | tail -n 1)
if [ -z "$ENDPOINT_LINE" ]; then
    echo "❌ Could not find endpoint in SSE response"
    exit 1
fi

# Extract the endpoint URL from the data line
MESSAGES_ENDPOINT=$(echo "$ENDPOINT_LINE" | sed 's/data: //')
echo "🎯 Messages endpoint: $MESSAGES_ENDPOINT"

# Step 2: Initialize MCP connection
echo ""
echo "🚀 Step 2: Initializing MCP connection..."
INIT_RESPONSE=$(curl -s -X POST "http://localhost:3002$MESSAGES_ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "initialize",
    "params": {
      "protocolVersion": "2024-11-05",
      "capabilities": {},
      "clientInfo": {
        "name": "test-client",
        "version": "1.0.0"
      }
    }
  }')

echo "📥 Initialize response:"
echo "$INIT_RESPONSE" | jq '.' 2>/dev/null || echo "$INIT_RESPONSE"

# Step 3: List available tools
echo ""
echo "📋 Step 3: Listing available tools..."
TOOLS_RESPONSE=$(curl -s -X POST "http://localhost:3002$MESSAGES_ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 2,
    "method": "tools/list"
  }')

echo "📥 Tools list response:"
echo "$TOOLS_RESPONSE" | jq '.' 2>/dev/null || echo "$TOOLS_RESPONSE"

# Step 4: Test kiotviet_get_categories tool
echo ""
echo "🧪 Step 4: Testing kiotviet_get_categories tool..."
CATEGORIES_RESPONSE=$(curl -s -X POST "http://localhost:3002$MESSAGES_ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 3,
    "method": "tools/call",
    "params": {
      "name": "kiotviet_get_categories",
      "arguments": {
        "orderDirection": "Desc",
        "pageSize": 5
      }
    }
  }')

echo "📥 Categories response:"
echo "$CATEGORIES_RESPONSE" | jq '.' 2>/dev/null || echo "$CATEGORIES_RESPONSE"

# Step 5: Test with different parameters
echo ""
echo "🔄 Step 5: Testing with hierarchical data..."
HIERARCHICAL_RESPONSE=$(curl -s -X POST "http://localhost:3002$MESSAGES_ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 4,
    "method": "tools/call",
    "params": {
      "name": "kiotviet_get_categories",
      "arguments": {
        "orderDirection": "Asc",
        "hierachicalData": true,
        "pageSize": 3
      }
    }
  }')

echo "📥 Hierarchical response:"
echo "$HIERARCHICAL_RESPONSE" | jq '.' 2>/dev/null || echo "$HIERARCHICAL_RESPONSE"

echo ""
echo "✅ All tests completed!"
echo ""
echo "💡 To run individual tests, use these curl commands:"
echo ""
echo "1. Get SSE endpoint:"
echo "   curl -s -N http://localhost:3002/sse | head -n 5"
echo ""
echo "2. Initialize (replace ENDPOINT with actual endpoint):"
echo "   curl -X POST 'http://localhost:3002/ENDPOINT' -H 'Content-Type: application/json' -d '{\"jsonrpc\":\"2.0\",\"id\":1,\"method\":\"initialize\",\"params\":{\"protocolVersion\":\"2024-11-05\",\"capabilities\":{},\"clientInfo\":{\"name\":\"test-client\",\"version\":\"1.0.0\"}}}'"
echo ""
echo "3. List tools:"
echo "   curl -X POST 'http://localhost:3002/ENDPOINT' -H 'Content-Type: application/json' -d '{\"jsonrpc\":\"2.0\",\"id\":2,\"method\":\"tools/list\"}'"
echo ""
echo "4. Call kiotviet_get_categories:"
echo "   curl -X POST 'http://localhost:3002/ENDPOINT' -H 'Content-Type: application/json' -d '{\"jsonrpc\":\"2.0\",\"id\":3,\"method\":\"tools/call\",\"params\":{\"name\":\"kiotviet_get_categories\",\"arguments\":{\"orderDirection\":\"Desc\",\"pageSize\":5}}}'"
