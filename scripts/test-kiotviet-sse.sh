#!/bin/bash

# Complete test script for KiotViet MCP SSE endpoint

echo "🧪 Testing KiotViet MCP via SSE endpoint..."
echo "=========================================="

# Check if server is running
if ! curl -s http://localhost:3002/sse > /dev/null 2>&1; then
    echo "❌ Server not running on port 3002"
    echo "Please start the server with: PORT=3002 bun run start:http"
    exit 1
fi

echo "✅ Server is running on port 3002"

# Create a temporary file for SSE output
SSE_OUTPUT=$(mktemp)
echo "📝 SSE output will be saved to: $SSE_OUTPUT"

# Start SSE connection in background and capture output
echo "🔌 Starting SSE connection..."
curl -s -N http://localhost:3002/sse > "$SSE_OUTPUT" &
SSE_PID=$!

# Wait a moment for connection to establish
sleep 2

# Extract session ID from SSE output
SESSION_ID=$(grep "sessionId=" "$SSE_OUTPUT" | head -n 1 | sed 's/.*sessionId=\([^&]*\).*/\1/')

if [ -z "$SESSION_ID" ]; then
    echo "❌ Could not extract session ID from SSE response"
    echo "SSE output:"
    cat "$SSE_OUTPUT"
    kill $SSE_PID 2>/dev/null
    rm "$SSE_OUTPUT"
    exit 1
fi

echo "🆔 Session ID: $SESSION_ID"

# Function to send MCP message
send_mcp_message() {
    local message="$1"
    local description="$2"
    
    echo ""
    echo "📤 $description"
    echo "Message: $message"
    
    curl -s -X POST "http://localhost:3002/messages?sessionId=$SESSION_ID" \
        -H "Content-Type: application/json" \
        -d "$message"
    
    # Wait a moment for response
    sleep 1
    
    echo ""
    echo "📥 SSE Response:"
    tail -n 10 "$SSE_OUTPUT" | grep -A 5 -B 5 "data:"
}

# Test 1: Initialize MCP connection
send_mcp_message '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "initialize",
    "params": {
        "protocolVersion": "2024-11-05",
        "capabilities": {},
        "clientInfo": {
            "name": "test-client",
            "version": "1.0.0"
        }
    }
}' "Initializing MCP connection"

# Test 2: List available tools
send_mcp_message '{
    "jsonrpc": "2.0",
    "id": 2,
    "method": "tools/list"
}' "Listing available tools"

# Test 3: Call kiotviet_get_categories with basic parameters
send_mcp_message '{
    "jsonrpc": "2.0",
    "id": 3,
    "method": "tools/call",
    "params": {
        "name": "kiotviet_get_categories",
        "arguments": {
            "orderDirection": "Desc",
            "pageSize": 5
        }
    }
}' "Testing kiotviet_get_categories (basic)"

# Test 4: Call kiotviet_get_categories with hierarchical data
send_mcp_message '{
    "jsonrpc": "2.0",
    "id": 4,
    "method": "tools/call",
    "params": {
        "name": "kiotviet_get_categories",
        "arguments": {
            "orderDirection": "Asc",
            "hierachicalData": true,
            "pageSize": 3,
            "currentItem": 0
        }
    }
}' "Testing kiotviet_get_categories (hierarchical)"

# Test 5: Call with invalid parameters (should fail)
send_mcp_message '{
    "jsonrpc": "2.0",
    "id": 5,
    "method": "tools/call",
    "params": {
        "name": "kiotviet_get_categories",
        "arguments": {
            "orderDirection": "Invalid",
            "pageSize": 150
        }
    }
}' "Testing kiotviet_get_categories (invalid params)"

echo ""
echo "⏳ Waiting for final responses..."
sleep 3

echo ""
echo "📋 Complete SSE Output:"
echo "======================="
cat "$SSE_OUTPUT"

# Cleanup
kill $SSE_PID 2>/dev/null
rm "$SSE_OUTPUT"

echo ""
echo "✅ Test completed!"
echo ""
echo "💡 Manual curl commands for testing:"
echo ""
echo "1. Get session ID:"
echo "   curl -s -N http://localhost:3002/sse | head -n 5"
echo ""
echo "2. Send tool call (replace SESSION_ID):"
echo "   curl -X POST 'http://localhost:3002/messages?sessionId=SESSION_ID' \\"
echo "     -H 'Content-Type: application/json' \\"
echo "     -d '{\"jsonrpc\":\"2.0\",\"id\":1,\"method\":\"tools/call\",\"params\":{\"name\":\"kiotviet_get_categories\",\"arguments\":{\"orderDirection\":\"Desc\",\"pageSize\":5}}}'"
echo ""
echo "3. Listen to SSE responses:"
echo "   curl -s -N http://localhost:3002/sse"
