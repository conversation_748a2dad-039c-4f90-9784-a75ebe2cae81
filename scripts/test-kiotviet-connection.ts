#!/usr/bin/env bun

/**
 * Test script to validate KiotViet API connection and authentication
 */

import { KiotVietGetCategoriesTool } from '../src/core/tools/kiotviet-get-categories.js';
import { Logger } from '../src/core/utils/logger.js';
import { validateEnvironment } from '../src/config/environment.js';

const logger = Logger.createContextLogger('TestKiotVietConnection');

async function testKiotVietConnection() {
  logger.info('Starting KiotViet connection test');

  try {
    // Validate environment configuration
    logger.info('Validating environment configuration...');
    const envValidation = validateEnvironment();
    if (!envValidation.valid) {
      logger.error('Environment validation failed', undefined, {
        errors: envValidation.errors,
      });
      process.exit(1);
    }
    logger.info('Environment configuration is valid');

    // Initialize the KiotViet Get Categories tool
    logger.info('Initializing KiotViet Get Categories tool...');
    const tool = new KiotVietGetCategoriesTool();
    await tool.initialize();
    logger.info('Tool initialized successfully');

    // Test health check
    logger.info('Testing health check...');
    const isHealthy = await tool.healthCheck();
    if (!isHealthy) {
      logger.error('Health check failed');
      process.exit(1);
    }
    logger.info('Health check passed');

    // Test getting categories
    logger.info('Testing get categories...');
    const result = await tool.execute({
      orderDirection: 'Asc',
      hierachicalData: false,
      pageSize: 5,
      currentItem: 0,
    });

    if (!result.success) {
      logger.error('Get categories test failed', undefined, {
        error: result.error,
      });
      process.exit(1);
    }

    logger.info('Get categories test passed', {
      totalCategories: result.metadata?.totalCategories,
      returnedCategories: result.metadata?.returnedCategories,
      executionTime: result.metadata?.executionTime,
    });

    // Display sample categories
    if (result.data?.categories && result.data.categories.length > 0) {
      logger.info('Sample categories retrieved:');
      result.data.categories.slice(0, 3).forEach((category: any, index: number) => {
        console.log(`  ${index + 1}. ${category.categoryName} (ID: ${category.categoryId})`);
      });
    }

    logger.info('All tests passed successfully! 🎉');
    process.exit(0);

  } catch (error) {
    logger.error('Test failed with error', error as Error);
    process.exit(1);
  }
}

// Run the test
testKiotVietConnection();
