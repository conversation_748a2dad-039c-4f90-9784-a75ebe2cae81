#!/bin/bash

# Test KiotViet API connection script

echo "🧪 Testing KiotViet API Connection..."
echo "=================================="

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ Error: .env file not found"
    echo "Please copy .env.example to .env and configure your KiotViet credentials"
    exit 1
fi

# Check if required environment variables are set
if [ -z "$KIOTVIET_RETAILER" ] || [ -z "$KIOTVIET_CLIENT_ID" ] || [ -z "$KIOTVIET_CLIENT_SECRET" ]; then
    echo "❌ Error: Missing required KiotViet environment variables"
    echo "Please ensure the following are set in your .env file:"
    echo "  - KIOTVIET_RETAILER"
    echo "  - KIOTVIET_CLIENT_ID"
    echo "  - KIOTVIET_CLIENT_SECRET"
    exit 1
fi

# Run the test
echo "🚀 Running connection test..."
bun run scripts/test-kiotviet-connection.ts

# Check the exit code
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ KiotViet connection test completed successfully!"
    echo "Your MCP server is ready to use with KiotViet API."
else
    echo ""
    echo "❌ KiotViet connection test failed!"
    echo "Please check your configuration and try again."
    exit 1
fi
