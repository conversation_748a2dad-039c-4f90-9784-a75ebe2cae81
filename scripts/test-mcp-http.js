#!/usr/bin/env node

/**
 * Test script for MCP HTTP/SSE endpoint
 */

import { EventSource } from 'eventsource';

class MCPClient {
  constructor(baseUrl) {
    this.baseUrl = baseUrl;
    this.sessionId = null;
    this.messagesEndpoint = null;
  }

  async connect() {
    return new Promise((resolve, reject) => {
      console.log('🔌 Connecting to MCP SSE endpoint...');
      
      const eventSource = new EventSource(`${this.baseUrl}/sse`);
      
      eventSource.onopen = () => {
        console.log('✅ SSE connection opened');
      };
      
      eventSource.onmessage = (event) => {
        console.log('📨 Received event:', event.type, event.data);
        
        if (event.type === 'endpoint') {
          this.messagesEndpoint = event.data;
          console.log('🎯 Messages endpoint:', this.messagesEndpoint);
          
          // Extract session ID from endpoint
          const match = this.messagesEndpoint.match(/sessionId=([^&]+)/);
          if (match) {
            this.sessionId = match[1];
            console.log('🆔 Session ID:', this.sessionId);
          }
          
          eventSource.close();
          resolve();
        }
      };
      
      eventSource.onerror = (error) => {
        console.error('❌ SSE connection error:', error);
        eventSource.close();
        reject(error);
      };
      
      // Timeout after 10 seconds
      setTimeout(() => {
        eventSource.close();
        reject(new Error('Connection timeout'));
      }, 10000);
    });
  }

  async sendMessage(message) {
    if (!this.messagesEndpoint) {
      throw new Error('Not connected. Call connect() first.');
    }

    const url = `${this.baseUrl}${this.messagesEndpoint}`;
    console.log('📤 Sending message to:', url);
    console.log('📝 Message:', JSON.stringify(message, null, 2));

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(message),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      console.log('📥 Response:', JSON.stringify(result, null, 2));
      return result;
    } catch (error) {
      console.error('❌ Request failed:', error.message);
      throw error;
    }
  }

  async initialize() {
    return this.sendMessage({
      jsonrpc: "2.0",
      id: 1,
      method: "initialize",
      params: {
        protocolVersion: "2024-11-05",
        capabilities: {},
        clientInfo: {
          name: "test-client",
          version: "1.0.0"
        }
      }
    });
  }

  async listTools() {
    return this.sendMessage({
      jsonrpc: "2.0",
      id: 2,
      method: "tools/list"
    });
  }

  async callTool(name, arguments_) {
    return this.sendMessage({
      jsonrpc: "2.0",
      id: 3,
      method: "tools/call",
      params: {
        name: name,
        arguments: arguments_
      }
    });
  }
}

async function testMCPServer() {
  const client = new MCPClient('http://localhost:3002');

  try {
    // Connect to SSE endpoint
    await client.connect();

    // Initialize MCP connection
    console.log('\n🚀 Initializing MCP connection...');
    await client.initialize();

    // List available tools
    console.log('\n📋 Listing available tools...');
    await client.listTools();

    // Test kiotviet_get_categories tool
    console.log('\n🧪 Testing kiotviet_get_categories tool...');
    await client.callTool('kiotviet_get_categories', {
      orderDirection: 'Desc',
      pageSize: 5
    });

    console.log('\n✅ All tests completed successfully!');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    process.exit(1);
  }
}

// EventSource should be available from import

// Run tests
testMCPServer();
