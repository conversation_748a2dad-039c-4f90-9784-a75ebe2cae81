#!/usr/bin/env bun

/**
 * Validation script to check if the KiotViet MCP Server setup is correct
 */

import { existsSync } from 'fs';
import { Logger } from '../src/core/utils/logger.js';
import { validateEnvironment } from '../src/config/environment.js';

const logger = Logger.createContextLogger('ValidateSetup');

interface ValidationResult {
  category: string;
  checks: Array<{
    name: string;
    passed: boolean;
    message?: string;
  }>;
}

async function validateSetup(): Promise<void> {
  logger.info('Starting KiotViet MCP Server setup validation');

  const results: ValidationResult[] = [];

  // File structure validation
  const fileStructureResult: ValidationResult = {
    category: 'File Structure',
    checks: []
  };

  const requiredFiles = [
    'src/config/environment.ts',
    'src/config/kiotviet-config.ts',
    'src/core/api/kiotviet-client.ts',
    'src/core/tools/kiotviet-get-categories.ts',
    'src/core/types/kiotviet.ts',
    'src/core/utils/logger.ts',
    'src/core/tools.ts',
    'src/server/server.ts',
    'src/index.ts',
    '.env.example',
    'package.json',
  ];

  for (const file of requiredFiles) {
    const exists = existsSync(file);
    fileStructureResult.checks.push({
      name: `File exists: ${file}`,
      passed: exists,
      message: exists ? undefined : `Missing required file: ${file}`
    });
  }

  results.push(fileStructureResult);

  // Environment validation
  const envResult: ValidationResult = {
    category: 'Environment Configuration',
    checks: []
  };

  const envFileExists = existsSync('.env');
  envResult.checks.push({
    name: '.env file exists',
    passed: envFileExists,
    message: envFileExists ? undefined : 'Create .env file from .env.example'
  });

  if (envFileExists) {
    const envValidation = validateEnvironment();
    envResult.checks.push({
      name: 'Environment variables valid',
      passed: envValidation.valid,
      message: envValidation.valid ? undefined : `Validation errors: ${envValidation.errors?.join(', ')}`
    });
  }

  results.push(envResult);

  // TypeScript compilation check
  const tsResult: ValidationResult = {
    category: 'TypeScript Compilation',
    checks: []
  };

  try {
    // Try to import main modules to check for compilation errors
    await import('../src/config/environment.js');
    tsResult.checks.push({
      name: 'Environment config compiles',
      passed: true
    });
  } catch (error) {
    tsResult.checks.push({
      name: 'Environment config compiles',
      passed: false,
      message: `Compilation error: ${(error as Error).message}`
    });
  }

  try {
    await import('../src/core/types/kiotviet.js');
    tsResult.checks.push({
      name: 'KiotViet types compile',
      passed: true
    });
  } catch (error) {
    tsResult.checks.push({
      name: 'KiotViet types compile',
      passed: false,
      message: `Compilation error: ${(error as Error).message}`
    });
  }

  results.push(tsResult);

  // Display results
  let allPassed = true;
  
  console.log('\n🔍 KiotViet MCP Server Setup Validation Results');
  console.log('================================================\n');

  for (const result of results) {
    const categoryPassed = result.checks.every(check => check.passed);
    const statusIcon = categoryPassed ? '✅' : '❌';
    
    console.log(`${statusIcon} ${result.category}`);
    
    for (const check of result.checks) {
      const checkIcon = check.passed ? '  ✓' : '  ✗';
      console.log(`${checkIcon} ${check.name}`);
      
      if (check.message) {
        console.log(`    ${check.message}`);
      }
    }
    
    console.log('');
    
    if (!categoryPassed) {
      allPassed = false;
    }
  }

  // Summary
  if (allPassed) {
    console.log('🎉 All validation checks passed!');
    console.log('Your KiotViet MCP Server setup is ready.');
    console.log('\nNext steps:');
    console.log('1. Configure your .env file with KiotViet credentials');
    console.log('2. Run: bun run test-kiotviet');
    console.log('3. Start the server: bun start');
  } else {
    console.log('❌ Some validation checks failed.');
    console.log('Please fix the issues above before proceeding.');
    process.exit(1);
  }
}

// Run validation
validateSetup().catch((error) => {
  logger.error('Validation failed with error', error);
  process.exit(1);
});
