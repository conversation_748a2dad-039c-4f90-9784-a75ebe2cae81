import*as e from"valibot";function t(e,t){return e?(e.push(t),e):[t]}function n(e,t){switch(t?.errorMode){case`ignore`:break;case`warn`:console.warn(e);break;default:throw Error(e)}}function r(e,r,i){if(i?.ignoreActions?.includes(r.type))return e;let a;switch(r.type){case`base64`:e.contentEncoding=`base64`;break;case`bic`:case`cuid2`:case`decimal`:case`digits`:case`emoji`:case`hexadecimal`:case`hex_color`:case`nanoid`:case`octal`:case`ulid`:e.pattern=r.requirement.source;break;case`description`:e.description=r.description;break;case`email`:e.format=`email`;break;case`empty`:e.type===`array`?e.maxItems=0:(e.type!==`string`&&(a=t(a,`The "${r.type}" action is not supported on type "${e.type}".`)),e.maxLength=0);break;case`entries`:e.minProperties=r.requirement,e.maxProperties=r.requirement;break;case`integer`:e.type=`integer`;break;case`ipv4`:e.format=`ipv4`;break;case`ipv6`:e.format=`ipv6`;break;case`iso_date`:e.format=`date`;break;case`iso_date_time`:case`iso_timestamp`:e.format=`date-time`;break;case`iso_time`:e.format=`time`;break;case`length`:e.type===`array`?(e.minItems=r.requirement,e.maxItems=r.requirement):(e.type!==`string`&&(a=t(a,`The "${r.type}" action is not supported on type "${e.type}".`)),e.minLength=r.requirement,e.maxLength=r.requirement);break;case`max_entries`:e.maxProperties=r.requirement;break;case`max_length`:e.type===`array`?e.maxItems=r.requirement:(e.type!==`string`&&(a=t(a,`The "${r.type}" action is not supported on type "${e.type}".`)),e.maxLength=r.requirement);break;case`max_value`:e.type!==`number`&&(a=t(a,`The "max_value" action is not supported on type "${e.type}".`)),e.maximum=r.requirement;break;case`metadata`:typeof r.metadata.title==`string`&&(e.title=r.metadata.title),typeof r.metadata.description==`string`&&(e.description=r.metadata.description),Array.isArray(r.metadata.examples)&&(e.examples=r.metadata.examples);break;case`min_entries`:e.minProperties=r.requirement;break;case`min_length`:e.type===`array`?e.minItems=r.requirement:(e.type!==`string`&&(a=t(a,`The "${r.type}" action is not supported on type "${e.type}".`)),e.minLength=r.requirement);break;case`min_value`:e.type!==`number`&&(a=t(a,`The "min_value" action is not supported on type "${e.type}".`)),e.minimum=r.requirement;break;case`multiple_of`:e.multipleOf=r.requirement;break;case`non_empty`:e.type===`array`?e.minItems=1:(e.type!==`string`&&(a=t(a,`The "${r.type}" action is not supported on type "${e.type}".`)),e.minLength=1);break;case`regex`:r.requirement.flags&&(a=t(a,`RegExp flags are not supported by JSON Schema.`)),e.pattern=r.requirement.source;break;case`title`:e.title=r.title;break;case`url`:e.format=`uri`;break;case`uuid`:e.format=`uuid`;break;case`value`:e.const=r.requirement;break;default:a=t(a,`The "${r.type}" action cannot be converted to JSON Schema.`)}if(i?.overrideAction){let t=i.overrideAction({valibotAction:r,jsonSchema:e,errors:a});if(t)return{...t}}if(a)for(let e of a)n(e,i);return e}function i(e){return e.flatMap(e=>`pipe`in e?i(e.pipe):e)}let a=0;function o(s,c,l,u,d=!1){if(!d){let e=u.referenceMap.get(c);if(e){if(s.$ref=`#/$defs/${e}`,l?.overrideRef){let t=l.overrideRef({...u,referenceId:e,valibotSchema:c,jsonSchema:s});t&&(s.$ref=t)}return s}}if(`pipe`in c){let e=i(c.pipe),t=0,a=e.length-1;if(l?.typeMode===`input`){let t=e.slice(1).findIndex(e=>e.kind===`schema`||e.kind===`transformation`&&(e.type===`find_item`||e.type===`parse_json`||e.type===`raw_transform`||e.type===`reduce_items`||e.type===`stringify_json`||e.type===`transform`));t!==-1&&(a=t)}else if(l?.typeMode===`output`){let n=e.findLastIndex(e=>e.kind===`schema`);n!==-1&&(t=n)}for(let i=t;i<=a;i++){let a=e[i];a.kind===`schema`?(i>t&&n(`Set the "typeMode" config to "input" or "output" to convert pipelines with multiple schemas.`,l),s=o(s,a,l,u,!0)):s=r(s,a,l)}return s}let f;switch(c.type){case`boolean`:s.type=`boolean`;break;case`null`:s.type=`null`;break;case`number`:s.type=`number`;break;case`string`:s.type=`string`;break;case`array`:s.type=`array`,s.items=o({},c.item,l,u);break;case`tuple`:case`tuple_with_rest`:case`loose_tuple`:case`strict_tuple`:s.type=`array`,s.items=[],s.minItems=c.items.length;for(let e of c.items)s.items.push(o({},e,l,u));c.type===`tuple_with_rest`?s.additionalItems=o({},c.rest,l,u):c.type===`strict_tuple`&&(s.additionalItems=!1);break;case`object`:case`object_with_rest`:case`loose_object`:case`strict_object`:for(let e in s.type=`object`,s.properties={},s.required=[],c.entries){let t=c.entries[e];s.properties[e]=o({},t,l,u),t.type!==`nullish`&&t.type!==`optional`&&s.required.push(e)}c.type===`object_with_rest`?s.additionalProperties=o({},c.rest,l,u):c.type===`strict_object`&&(s.additionalProperties=!1);break;case`record`:`pipe`in c.key&&(f=t(f,`The "record" schema with a schema for the key that contains a "pipe" cannot be converted to JSON Schema.`)),c.key.type!==`string`&&(f=t(f,`The "record" schema with the "${c.key.type}" schema for the key cannot be converted to JSON Schema.`)),s.type=`object`,s.additionalProperties=o({},c.value,l,u);break;case`any`:case`unknown`:break;case`nullable`:case`nullish`:s.anyOf=[o({},c.wrapped,l,u),{type:`null`}],c.default!==void 0&&(s.default=e.getDefault(c));break;case`exact_optional`:case`optional`:case`undefinedable`:s=o(s,c.wrapped,l,u),c.default!==void 0&&(s.default=e.getDefault(c));break;case`literal`:typeof c.literal!=`boolean`&&typeof c.literal!=`number`&&typeof c.literal!=`string`&&(f=t(f,`The value of the "literal" schema is not JSON compatible.`)),s.const=c.literal;break;case`enum`:s.enum=c.options;break;case`picklist`:c.options.some(e=>typeof e!=`number`&&typeof e!=`string`)&&(f=t(f,`An option of the "picklist" schema is not JSON compatible.`)),s.enum=c.options;break;case`union`:case`variant`:s.anyOf=c.options.map(e=>o({},e,l,u));break;case`intersect`:s.allOf=c.options.map(e=>o({},e,l,u));break;case`lazy`:{let e=u.getterMap.get(c.getter);e||(e=c.getter(void 0),u.getterMap.set(c.getter,e));let t=u.referenceMap.get(e);if(t||(t=`${a++}`,u.referenceMap.set(e,t),u.definitions[t]=o({},e,l,u,!0)),s.$ref=`#/$defs/${t}`,l?.overrideRef){let n=l.overrideRef({...u,referenceId:t,valibotSchema:e,jsonSchema:s});n&&(s.$ref=n)}break}default:f=t(f,`The "${c.type}" schema cannot be converted to JSON Schema.`)}if(l?.overrideSchema){let e=l.overrideSchema({...u,referenceId:u.referenceMap.get(c),valibotSchema:c,jsonSchema:s,errors:f});if(e)return{...e}}if(f)for(let e of f)n(e,l);return s}let s;function c(e){s={...s??{},...e}}function l(){return s}function u(e,t){let n={definitions:{},referenceMap:new Map,getterMap:new Map},r=t?.definitions??l();if(r){for(let e in r)n.referenceMap.set(r[e],e);for(let e in r)n.definitions[e]=o({},r[e],t,n,!0)}let i=o({$schema:`http://json-schema.org/draft-07/schema#`},e,t,n);return n.referenceMap.size&&(i.$defs=n.definitions),i}function d(e,t){let n={definitions:{},referenceMap:new Map,getterMap:new Map};for(let t in e)n.referenceMap.set(e[t],t);for(let r in e)n.definitions[r]=o({},e[r],t,n,!0);return n.definitions}export{c as addGlobalDefs,l as getGlobalDefs,u as toJsonSchema,d as toJsonSchemaDefs};