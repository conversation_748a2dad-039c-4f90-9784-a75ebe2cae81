var e=Object.create,t=Object.defineProperty,n=Object.getOwnPropertyDescriptor,r=Object.getOwnPropertyNames,i=Object.getPrototypeOf,a=Object.prototype.hasOwnProperty,o=(e,i,o,s)=>{if(i&&typeof i==`object`||typeof i==`function`)for(var c=r(i),l=0,u=c.length,d;l<u;l++)d=c[l],!a.call(e,d)&&d!==o&&t(e,d,{get:(e=>i[e]).bind(null,d),enumerable:!(s=n(i,d))||s.enumerable});return e},s=(n,r,a)=>(a=n==null?{}:e(i(n)),o(r||!n||!n.__esModule?t(a,`default`,{value:n,enumerable:!0}):a,n));const c=s(require(`valibot`));function l(e,t){return e?(e.push(t),e):[t]}function u(e,t){switch(t?.errorMode){case`ignore`:break;case`warn`:console.warn(e);break;default:throw Error(e)}}function d(e,t,n){if(n?.ignoreActions?.includes(t.type))return e;let r;switch(t.type){case`base64`:e.contentEncoding=`base64`;break;case`bic`:case`cuid2`:case`decimal`:case`digits`:case`emoji`:case`hexadecimal`:case`hex_color`:case`nanoid`:case`octal`:case`ulid`:e.pattern=t.requirement.source;break;case`description`:e.description=t.description;break;case`email`:e.format=`email`;break;case`empty`:e.type===`array`?e.maxItems=0:(e.type!==`string`&&(r=l(r,`The "${t.type}" action is not supported on type "${e.type}".`)),e.maxLength=0);break;case`entries`:e.minProperties=t.requirement,e.maxProperties=t.requirement;break;case`integer`:e.type=`integer`;break;case`ipv4`:e.format=`ipv4`;break;case`ipv6`:e.format=`ipv6`;break;case`iso_date`:e.format=`date`;break;case`iso_date_time`:case`iso_timestamp`:e.format=`date-time`;break;case`iso_time`:e.format=`time`;break;case`length`:e.type===`array`?(e.minItems=t.requirement,e.maxItems=t.requirement):(e.type!==`string`&&(r=l(r,`The "${t.type}" action is not supported on type "${e.type}".`)),e.minLength=t.requirement,e.maxLength=t.requirement);break;case`max_entries`:e.maxProperties=t.requirement;break;case`max_length`:e.type===`array`?e.maxItems=t.requirement:(e.type!==`string`&&(r=l(r,`The "${t.type}" action is not supported on type "${e.type}".`)),e.maxLength=t.requirement);break;case`max_value`:e.type!==`number`&&(r=l(r,`The "max_value" action is not supported on type "${e.type}".`)),e.maximum=t.requirement;break;case`metadata`:typeof t.metadata.title==`string`&&(e.title=t.metadata.title),typeof t.metadata.description==`string`&&(e.description=t.metadata.description),Array.isArray(t.metadata.examples)&&(e.examples=t.metadata.examples);break;case`min_entries`:e.minProperties=t.requirement;break;case`min_length`:e.type===`array`?e.minItems=t.requirement:(e.type!==`string`&&(r=l(r,`The "${t.type}" action is not supported on type "${e.type}".`)),e.minLength=t.requirement);break;case`min_value`:e.type!==`number`&&(r=l(r,`The "min_value" action is not supported on type "${e.type}".`)),e.minimum=t.requirement;break;case`multiple_of`:e.multipleOf=t.requirement;break;case`non_empty`:e.type===`array`?e.minItems=1:(e.type!==`string`&&(r=l(r,`The "${t.type}" action is not supported on type "${e.type}".`)),e.minLength=1);break;case`regex`:t.requirement.flags&&(r=l(r,`RegExp flags are not supported by JSON Schema.`)),e.pattern=t.requirement.source;break;case`title`:e.title=t.title;break;case`url`:e.format=`uri`;break;case`uuid`:e.format=`uuid`;break;case`value`:e.const=t.requirement;break;default:r=l(r,`The "${t.type}" action cannot be converted to JSON Schema.`)}if(n?.overrideAction){let i=n.overrideAction({valibotAction:t,jsonSchema:e,errors:r});if(i)return{...i}}if(r)for(let e of r)u(e,n);return e}function f(e){return e.flatMap(e=>`pipe`in e?f(e.pipe):e)}let p=0;function m(e,t,n,r,i=!1){if(!i){let i=r.referenceMap.get(t);if(i){if(e.$ref=`#/$defs/${i}`,n?.overrideRef){let a=n.overrideRef({...r,referenceId:i,valibotSchema:t,jsonSchema:e});a&&(e.$ref=a)}return e}}if(`pipe`in t){let i=f(t.pipe),a=0,o=i.length-1;if(n?.typeMode===`input`){let e=i.slice(1).findIndex(e=>e.kind===`schema`||e.kind===`transformation`&&(e.type===`find_item`||e.type===`parse_json`||e.type===`raw_transform`||e.type===`reduce_items`||e.type===`stringify_json`||e.type===`transform`));e!==-1&&(o=e)}else if(n?.typeMode===`output`){let e=i.findLastIndex(e=>e.kind===`schema`);e!==-1&&(a=e)}for(let t=a;t<=o;t++){let o=i[t];o.kind===`schema`?(t>a&&u(`Set the "typeMode" config to "input" or "output" to convert pipelines with multiple schemas.`,n),e=m(e,o,n,r,!0)):e=d(e,o,n)}return e}let a;switch(t.type){case`boolean`:e.type=`boolean`;break;case`null`:e.type=`null`;break;case`number`:e.type=`number`;break;case`string`:e.type=`string`;break;case`array`:e.type=`array`,e.items=m({},t.item,n,r);break;case`tuple`:case`tuple_with_rest`:case`loose_tuple`:case`strict_tuple`:e.type=`array`,e.items=[],e.minItems=t.items.length;for(let i of t.items)e.items.push(m({},i,n,r));t.type===`tuple_with_rest`?e.additionalItems=m({},t.rest,n,r):t.type===`strict_tuple`&&(e.additionalItems=!1);break;case`object`:case`object_with_rest`:case`loose_object`:case`strict_object`:for(let i in e.type=`object`,e.properties={},e.required=[],t.entries){let a=t.entries[i];e.properties[i]=m({},a,n,r),a.type!==`nullish`&&a.type!==`optional`&&e.required.push(i)}t.type===`object_with_rest`?e.additionalProperties=m({},t.rest,n,r):t.type===`strict_object`&&(e.additionalProperties=!1);break;case`record`:`pipe`in t.key&&(a=l(a,`The "record" schema with a schema for the key that contains a "pipe" cannot be converted to JSON Schema.`)),t.key.type!==`string`&&(a=l(a,`The "record" schema with the "${t.key.type}" schema for the key cannot be converted to JSON Schema.`)),e.type=`object`,e.additionalProperties=m({},t.value,n,r);break;case`any`:case`unknown`:break;case`nullable`:case`nullish`:e.anyOf=[m({},t.wrapped,n,r),{type:`null`}],t.default!==void 0&&(e.default=c.getDefault(t));break;case`exact_optional`:case`optional`:case`undefinedable`:e=m(e,t.wrapped,n,r),t.default!==void 0&&(e.default=c.getDefault(t));break;case`literal`:typeof t.literal!=`boolean`&&typeof t.literal!=`number`&&typeof t.literal!=`string`&&(a=l(a,`The value of the "literal" schema is not JSON compatible.`)),e.const=t.literal;break;case`enum`:e.enum=t.options;break;case`picklist`:t.options.some(e=>typeof e!=`number`&&typeof e!=`string`)&&(a=l(a,`An option of the "picklist" schema is not JSON compatible.`)),e.enum=t.options;break;case`union`:case`variant`:e.anyOf=t.options.map(e=>m({},e,n,r));break;case`intersect`:e.allOf=t.options.map(e=>m({},e,n,r));break;case`lazy`:{let i=r.getterMap.get(t.getter);i||(i=t.getter(void 0),r.getterMap.set(t.getter,i));let a=r.referenceMap.get(i);if(a||(a=`${p++}`,r.referenceMap.set(i,a),r.definitions[a]=m({},i,n,r,!0)),e.$ref=`#/$defs/${a}`,n?.overrideRef){let t=n.overrideRef({...r,referenceId:a,valibotSchema:i,jsonSchema:e});t&&(e.$ref=t)}break}default:a=l(a,`The "${t.type}" schema cannot be converted to JSON Schema.`)}if(n?.overrideSchema){let i=n.overrideSchema({...r,referenceId:r.referenceMap.get(t),valibotSchema:t,jsonSchema:e,errors:a});if(i)return{...i}}if(a)for(let e of a)u(e,n);return e}let h;function g(e){h={...h??{},...e}}function _(){return h}function v(e,t){let n={definitions:{},referenceMap:new Map,getterMap:new Map},r=t?.definitions??_();if(r){for(let e in r)n.referenceMap.set(r[e],e);for(let e in r)n.definitions[e]=m({},r[e],t,n,!0)}let i=m({$schema:`http://json-schema.org/draft-07/schema#`},e,t,n);return n.referenceMap.size&&(i.$defs=n.definitions),i}function y(e,t){let n={definitions:{},referenceMap:new Map,getterMap:new Map};for(let t in e)n.referenceMap.set(e[t],t);for(let r in e)n.definitions[r]=m({},e[r],t,n,!0);return n.definitions}exports.addGlobalDefs=g,exports.getGlobalDefs=_,exports.toJsonSchema=v,exports.toJsonSchemaDefs=y;