import { JSONSchema7, JSONSchema7 as JSONSchema7$1 } from "json-schema";

//#region ../../library/src/types/metadata.d.ts
/**
* Base metadata interface.
*/
interface BaseMetadata<TInput> {
  /**
  * The object kind.
  */
  readonly kind: "metadata";
  /**
  * The metadata type.
  */
  readonly type: string;
  /**
  * The metadata reference.
  */
  readonly reference: (...args: any[]) => BaseMetadata<any>;
  /**
  * The input, output and issue type.
  *
  * @internal
  */
  readonly "~types"?: {
    readonly input: TInput;
    readonly output: TInput;
    readonly issue: never;
  } | undefined;
}
/**
* Generic metadata type.
*/
// eslint-disable-next-line @typescript-eslint/no-explicit-any
//#endregion
//#region ../../library/src/types/dataset.d.ts
/**
* Unknown dataset interface.
*/
interface UnknownDataset {
  /**
  * Whether is's typed.
  */
  typed?: false;
  /**
  * The dataset value.
  */
  value: unknown;
  /**
  * The dataset issues.
  */
  issues?: undefined;
}
/**
* Success dataset interface.
*/
interface SuccessDataset<TValue> {
  /**
  * Whether is's typed.
  */
  typed: true;
  /**
  * The dataset value.
  */
  value: TValue;
  /**
  * The dataset issues.
  */
  issues?: undefined;
}
/**
* Partial dataset interface.
*/
interface PartialDataset<TValue, TIssue extends BaseIssue<unknown>> {
  /**
  * Whether is's typed.
  */
  typed: true;
  /**
  * The dataset value.
  */
  value: TValue;
  /**
  * The dataset issues.
  */
  issues: [TIssue, ...TIssue[]];
}
/**
* Failure dataset interface.
*/
interface FailureDataset<TIssue extends BaseIssue<unknown>> {
  /**
  * Whether is's typed.
  */
  typed: false;
  /**
  * The dataset value.
  */
  value: unknown;
  /**
  * The dataset issues.
  */
  issues: [TIssue, ...TIssue[]];
}
/**
* Output dataset type.
*/
type OutputDataset<TValue, TIssue extends BaseIssue<unknown>> = SuccessDataset<TValue> | PartialDataset<TValue, TIssue> | FailureDataset<TIssue>;
//#endregion
//#region ../../library/src/types/standard.d.ts
/**
* The Standard Schema properties interface.
*/
interface StandardProps<TInput, TOutput> {
  /**
  * The version number of the standard.
  */
  readonly version: 1;
  /**
  * The vendor name of the schema library.
  */
  readonly vendor: "valibot";
  /**
  * Validates unknown input values.
  */
  readonly validate: (value: unknown) => StandardResult<TOutput> | Promise<StandardResult<TOutput>>;
  /**
  * Inferred types associated with the schema.
  */
  readonly types?: StandardTypes<TInput, TOutput> | undefined;
}
/**
* The result interface of the validate function.
*/
type StandardResult<TOutput> = StandardSuccessResult<TOutput> | StandardFailureResult;
/**
* The result interface if validation succeeds.
*/
interface StandardSuccessResult<TOutput> {
  /**
  * The typed output value.
  */
  readonly value: TOutput;
  /**
  * The non-existent issues.
  */
  readonly issues?: undefined;
}
/**
* The result interface if validation fails.
*/
interface StandardFailureResult {
  /**
  * The issues of failed validation.
  */
  readonly issues: readonly StandardIssue[];
}
/**
* The issue interface of the failure output.
*/
interface StandardIssue {
  /**
  * The error message of the issue.
  */
  readonly message: string;
  /**
  * The path of the issue, if any.
  */
  readonly path?: readonly (PropertyKey | StandardPathItem)[] | undefined;
}
/**
* The path item interface of the issue.
*/
interface StandardPathItem {
  /**
  * The key of the path item.
  */
  readonly key: PropertyKey;
}
/**
* The Standard Schema types interface.
*/
interface StandardTypes<TInput, TOutput> {
  /**
  * The input type of the schema.
  */
  readonly input: TInput;
  /**
  * The output type of the schema.
  */
  readonly output: TOutput;
}
//#endregion
//#region ../../library/src/types/schema.d.ts
/**
* Base schema interface.
*/
interface BaseSchema<TInput, TOutput, TIssue extends BaseIssue<unknown>> {
  /**
  * The object kind.
  */
  readonly kind: "schema";
  /**
  * The schema type.
  */
  readonly type: string;
  /**
  * The schema reference.
  */
  readonly reference: (...args: any[]) => BaseSchema<unknown, unknown, BaseIssue<unknown>>;
  /**
  * The expected property.
  */
  readonly expects: string;
  /**
  * Whether it's async.
  */
  readonly async: false;
  /**
  * The Standard Schema properties.
  *
  * @internal
  */
  readonly "~standard": StandardProps<TInput, TOutput>;
  /**
  * Parses unknown input values.
  *
  * @param dataset The input dataset.
  * @param config The configuration.
  *
  * @returns The output dataset.
  *
  * @internal
  */
  readonly "~run": (dataset: UnknownDataset, config: Config<BaseIssue<unknown>>) => OutputDataset<TOutput, TIssue>;
  /**
  * The input, output and issue type.
  *
  * @internal
  */
  readonly "~types"?: {
    readonly input: TInput;
    readonly output: TOutput;
    readonly issue: TIssue;
  } | undefined;
}
/**
* Base schema async interface.
*/
//#endregion
//#region ../../library/src/types/transformation.d.ts
/**
* Base transformation interface.
*/
interface BaseTransformation<TInput, TOutput, TIssue extends BaseIssue<unknown>> {
  /**
  * The object kind.
  */
  readonly kind: "transformation";
  /**
  * The transformation type.
  */
  readonly type: string;
  /**
  * The transformation reference.
  */
  readonly reference: (...args: any[]) => BaseTransformation<any, any, BaseIssue<unknown>>;
  /**
  * Whether it's async.
  */
  readonly async: false;
  /**
  * Transforms known input values.
  *
  * @param dataset The input dataset.
  * @param config The configuration.
  *
  * @returns The output dataset.
  *
  * @internal
  */
  readonly "~run": (dataset: SuccessDataset<TInput>, config: Config<BaseIssue<unknown>>) => OutputDataset<TOutput, BaseIssue<unknown> | TIssue>;
  /**
  * The input, output and issue type.
  *
  * @internal
  */
  readonly "~types"?: {
    readonly input: TInput;
    readonly output: TOutput;
    readonly issue: TIssue;
  } | undefined;
}
/**
* Base transformation async interface.
*/
//#endregion
//#region ../../library/src/types/validation.d.ts
/**
* Base validation interface.
*/
interface BaseValidation<TInput, TOutput, TIssue extends BaseIssue<unknown>> {
  /**
  * The object kind.
  */
  readonly kind: "validation";
  /**
  * The validation type.
  */
  readonly type: string;
  /**
  * The validation reference.
  */
  readonly reference: (...args: any[]) => BaseValidation<any, any, BaseIssue<unknown>>;
  /**
  * The expected property.
  */
  readonly expects: string | null;
  /**
  * Whether it's async.
  */
  readonly async: false;
  /**
  * Validates known input values.
  *
  * @param dataset The input dataset.
  * @param config The configuration.
  *
  * @returns The output dataset.
  *
  * @internal
  */
  readonly "~run": (dataset: OutputDataset<TInput, BaseIssue<unknown>>, config: Config<BaseIssue<unknown>>) => OutputDataset<TOutput, BaseIssue<unknown> | TIssue>;
  /**
  * The input, output and issue type.
  *
  * @internal
  */
  readonly "~types"?: {
    readonly input: TInput;
    readonly output: TOutput;
    readonly issue: TIssue;
  } | undefined;
}
/**
* Base validation async interface.
*/
//#endregion
//#region ../../library/src/types/utils.d.ts
/**
* Constructs a type that is maybe readonly.
*/
type MaybeReadonly<TValue> = TValue | Readonly<TValue>;
/**
* Constructs a type that is maybe a promise.
*/
//#endregion
//#region ../../library/src/types/other.d.ts
/**
* Error message type.
*/
type ErrorMessage<TIssue extends BaseIssue<unknown>> = ((issue: TIssue) => string) | string;
/**
* Default type.
*/
//#endregion
//#region ../../library/src/types/issue.d.ts
/**
* Array path item interface.
*/
interface ArrayPathItem {
  /**
  * The path item type.
  */
  readonly type: "array";
  /**
  * The path item origin.
  */
  readonly origin: "value";
  /**
  * The path item input.
  */
  readonly input: MaybeReadonly<unknown[]>;
  /**
  * The path item key.
  */
  readonly key: number;
  /**
  * The path item value.
  */
  readonly value: unknown;
}
/**
* Map path item interface.
*/
interface MapPathItem {
  /**
  * The path item type.
  */
  readonly type: "map";
  /**
  * The path item origin.
  */
  readonly origin: "key" | "value";
  /**
  * The path item input.
  */
  readonly input: Map<unknown, unknown>;
  /**
  * The path item key.
  */
  readonly key: unknown;
  /**
  * The path item value.
  */
  readonly value: unknown;
}
/**
* Object path item interface.
*/
interface ObjectPathItem {
  /**
  * The path item type.
  */
  readonly type: "object";
  /**
  * The path item origin.
  */
  readonly origin: "key" | "value";
  /**
  * The path item input.
  */
  readonly input: Record<string, unknown>;
  /**
  * The path item key.
  */
  readonly key: string;
  /**
  * The path item value.
  */
  readonly value: unknown;
}
/**
* Set path item interface.
*/
interface SetPathItem {
  /**
  * The path item type.
  */
  readonly type: "set";
  /**
  * The path item origin.
  */
  readonly origin: "value";
  /**
  * The path item input.
  */
  readonly input: Set<unknown>;
  /**
  * The path item key.
  */
  readonly key: null;
  /**
  * The path item key.
  */
  readonly value: unknown;
}
/**
* Unknown path item interface.
*/
interface UnknownPathItem {
  /**
  * The path item type.
  */
  readonly type: "unknown";
  /**
  * The path item origin.
  */
  readonly origin: "key" | "value";
  /**
  * The path item input.
  */
  readonly input: unknown;
  /**
  * The path item key.
  */
  readonly key: unknown;
  /**
  * The path item value.
  */
  readonly value: unknown;
}
/**
* Issue path item type.
*/
type IssuePathItem = ArrayPathItem | MapPathItem | ObjectPathItem | SetPathItem | UnknownPathItem;
/**
* Base issue interface.
*/
interface BaseIssue<TInput> extends Config<BaseIssue<TInput>> {
  /**
  * The issue kind.
  */
  readonly kind: "schema" | "validation" | "transformation";
  /**
  * The issue type.
  */
  readonly type: string;
  /**
  * The raw input data.
  */
  readonly input: TInput;
  /**
  * The expected property.
  */
  readonly expected: string | null;
  /**
  * The received property.
  */
  readonly received: string;
  /**
  * The error message.
  */
  readonly message: string;
  /**
  * The input requirement.
  */
  readonly requirement?: unknown | undefined;
  /**
  * The issue path.
  */
  readonly path?: [IssuePathItem, ...IssuePathItem[]] | undefined;
  /**
  * The sub issues.
  */
  readonly issues?: [BaseIssue<TInput>, ...BaseIssue<TInput>[]] | undefined;
}
/**
* Generic issue type.
*/
//#endregion
//#region ../../library/src/types/config.d.ts
/**
* Config interface.
*/
interface Config<TIssue extends BaseIssue<unknown>> {
  /**
  * The selected language.
  */
  readonly lang?: string | undefined;
  /**
  * The error message.
  */
  readonly message?: ErrorMessage<TIssue> | undefined;
  /**
  * Whether it should be aborted early.
  */
  readonly abortEarly?: boolean | undefined;
  /**
  * Whether a pipe should be aborted early.
  */
  readonly abortPipeEarly?: boolean | undefined;
}
//#endregion
//#region ../../library/src/types/pipe.d.ts
/**
* Pipe action type.
*/
type PipeAction<TInput, TOutput, TIssue extends BaseIssue<unknown>> = BaseValidation<TInput, TOutput, TIssue> | BaseTransformation<TInput, TOutput, TIssue> | BaseMetadata<TInput>;
/**
* Pipe action async type.
*/
//#endregion
//#region src/type.d.ts
/**
* JSON Schema conversion context interface.
*/
interface ConversionContext {
  /**
  * The JSON Schema definitions that have already been created.
  */
  readonly definitions: Record<string, JSONSchema7$1>;
  /**
  * The JSON Schema reference map that is used to look up the reference ID
  * for a given Valibot schema.
  */
  readonly referenceMap: Map<BaseSchema<unknown, unknown, BaseIssue<unknown>>, string>;
  /**
  * The lazy schema getter map that is used internally to ensure that
  * recursive lazy schemas are unwrapped only once.
  */
  readonly getterMap: Map<(input: unknown) => BaseSchema<unknown, unknown, BaseIssue<unknown>>, BaseSchema<unknown, unknown, BaseIssue<unknown>>>;
}
/**
* JSON Schema override context interface for schemas.
*
* @beta
*/
interface OverrideSchemaContext extends ConversionContext {
  /**
  * The JSON Schema reference ID.
  */
  readonly referenceId: string | undefined;
  /**
  * The Valibot schema to be converted.
  */
  readonly valibotSchema: BaseSchema<unknown, unknown, BaseIssue<unknown>>;
  /**
  * The converted JSON Schema.
  */
  readonly jsonSchema: JSONSchema7$1;
  /**
  * The errors of the current Valibot schema conversion.
  */
  readonly errors: [string, ...string[]] | undefined;
}
/**
* JSON Schema override context interface for actions.
*
* @beta
*/
interface OverrideActionContext {
  /**
  * The Valibot action to be converted.
  */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  readonly valibotAction: PipeAction<any, any, BaseIssue<unknown>>;
  /**
  * The converted JSON Schema.
  */
  readonly jsonSchema: JSONSchema7$1;
  /**
  * The errors of the current Valibot action conversion.
  */
  readonly errors: [string, ...string[]] | undefined;
}
/**
* JSON Schema override context interface for references.
*
* @beta
*/
interface OverrideRefContext extends ConversionContext {
  /**
  * The JSON Schema reference ID.
  */
  readonly referenceId: string;
  /**
  * The Valibot schema to be converted.
  */
  readonly valibotSchema: BaseSchema<unknown, unknown, BaseIssue<unknown>>;
  /**
  * The converted JSON Schema.
  */
  readonly jsonSchema: JSONSchema7$1;
}
/**
* JSON Schema conversion config interface.
*/
interface ConversionConfig {
  /**
  * Whether to convert the input or output type of the Valibot schema to JSON Schema.
  *
  * When set to 'input', conversion stops before the first potential type
  * transformation action or second schema in any pipeline.
  *
  * When set to 'output', conversion of any pipelines starts from the last
  * schema in the pipeline. Therefore, the output type must be specified
  * explicitly with a schema after the last type transformation action.
  *
  * @beta
  */
  readonly typeMode?: "ignore" | "input" | "output";
  /**
  * The policy for handling incompatible schemas and actions.
  */
  readonly errorMode?: "throw" | "warn" | "ignore";
  /**
  * The schema definitions for constructing recursive schemas. If not
  * specified, the definitions are generated automatically as needed.
  */
  readonly definitions?: Record<string, BaseSchema<unknown, unknown, BaseIssue<unknown>>>;
  /**
  * Overrides the JSON Schema conversion for a specific Valibot schema.
  *
  * Only return a JSON Schema if you want to override the default conversion
  * behaviour and suppress errors for a specific schema. Returning either
  * `null` or `undefined` will skip the override.
  *
  * @param context The conversion context.
  *
  * @returns A JSON Schema, if overridden.
  *
  * @beta
  */
  readonly overrideSchema?: (context: OverrideSchemaContext) => JSONSchema7$1 | null | undefined;
  /**
  * The actions that should be ignored during the conversion.
  *
  * @beta
  */
  readonly ignoreActions?: string[];
  /**
  * Overrides the JSON Schema reference for a specific Valibot action.
  *
  * Only return a JSON Schema if you want to override the default conversion
  * behaviour and suppress errors for a specific action. Returning either
  * `null` or `undefined` will skip the override.
  *
  * @param context The conversion context.
  *
  * @returns A JSON Schema, if overridden.
  *
  * @beta
  */
  readonly overrideAction?: (context: OverrideActionContext) => JSONSchema7$1 | null | undefined;
  /**
  * Overrides the JSON Schema reference for a specific reference ID.
  *
  * @param context The conversion context.
  *
  * @returns A reference ID, if overridden.
  *
  * @beta
  */
  readonly overrideRef?: (context: OverrideRefContext) => string | null | undefined;
}
//#endregion
//#region src/functions/toJsonSchema/toJsonSchema.d.ts
/**
* Converts a Valibot schema to the JSON Schema format.
*
* @param schema The Valibot schema object.
* @param config The JSON Schema configuration.
*
* @returns The converted JSON Schema.
*/
declare function toJsonSchema(schema: BaseSchema<unknown, unknown, BaseIssue<unknown>>, config?: ConversionConfig): JSONSchema7$1;
//#endregion
//#region src/functions/toJsonSchemaDefs/toJsonSchemaDefs.d.ts
/**
* Converts Valibot schema definitions to JSON Schema definitions.
*
* @param definitions The Valibot schema definitions.
* @param config The JSON Schema configuration.
*
* @returns The converted JSON Schema definitions.
*/
declare function toJsonSchemaDefs<TDefinitions extends Record<string, BaseSchema<unknown, unknown, BaseIssue<unknown>>>>(definitions: TDefinitions, config?: Omit<ConversionConfig, "definitions">): { [TKey in keyof TDefinitions]: JSONSchema7$1 };
//#endregion
//#region src/storages/globalDefs/globalDefs.d.ts
/**
* Adds new definitions to the global schema definitions.
*
* @param definitions The schema definitions.
*
* @beta
*/
declare function addGlobalDefs(definitions: Record<string, BaseSchema<unknown, unknown, BaseIssue<unknown>>>): void;
/**
* Returns the current global schema definitions.
*
* @returns The schema definitions.
*
* @beta
*/
declare function getGlobalDefs(): Record<string, BaseSchema<unknown, unknown, BaseIssue<unknown>>> | undefined;
//#endregion
export { ConversionConfig, ConversionContext, JSONSchema7, OverrideActionContext, OverrideRefContext, OverrideSchemaContext, addGlobalDefs, getGlobalDefs, toJsonSchema, toJsonSchemaDefs };