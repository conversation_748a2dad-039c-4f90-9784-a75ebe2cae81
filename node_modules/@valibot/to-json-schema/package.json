{"name": "@valibot/to-json-schema", "description": "The official JSON schema converter for Valibot", "version": "1.3.0", "license": "MIT", "author": "<PERSON>", "homepage": "https://valibot.dev", "contributors": [{"name": "<PERSON>"}], "repository": {"type": "git", "url": "https://github.com/fabian-hiller/valibot"}, "keywords": ["valibot", "schema", "converter", "json-schema"], "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "sideEffects": false, "files": ["dist"], "publishConfig": {"access": "public"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/json-schema": "^7.0.15", "@types/node": "^22.13.5", "@vitest/coverage-v8": "^3.0.7", "eslint": "^9.21.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsdoc": "^50.6.3", "eslint-plugin-security": "^3.0.1", "globals": "^16.0.0", "tsdown": "^0.12.4", "typescript": "^5.7.3", "typescript-eslint": "^8.25.0", "valibot": "^1.1.0", "vite": "^6.2.0", "vite-tsconfig-paths": "^5.1.4", "vitest": "3.0.7"}, "peerDependencies": {"valibot": "^1.1.0"}, "scripts": {"test": "vitest --typecheck", "coverage": "vitest run --coverage --isolate", "lint": "eslint \"src/**/*.ts*\" && tsc --noEmit && deno check ./src/index.ts", "lint.fix": "eslint \"src/**/*.ts*\" --fix", "format": "prettier --write ./src", "format.check": "prettier --check ./src", "build": "tsdown"}}