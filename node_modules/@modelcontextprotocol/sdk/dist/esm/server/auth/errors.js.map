{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../../../../src/server/auth/errors.ts"], "names": [], "mappings": "AAEA;;GAEG;AACH,MAAM,OAAO,UAAW,SAAQ,KAAK;IAGnC,YACE,OAAe,EACC,QAAiB;QAEjC,KAAK,CAAC,OAAO,CAAC,CAAC;QAFC,aAAQ,GAAR,QAAQ,CAAS;QAGjC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,MAAM,QAAQ,GAAuB;YACnC,KAAK,EAAE,IAAI,CAAC,SAAS;YACrB,iBAAiB,EAAE,IAAI,CAAC,OAAO;SAChC,CAAC;QAEF,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC;QACrC,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,IAAI,SAAS;QACX,OAAQ,IAAI,CAAC,WAAiC,CAAC,SAAS,CAAA;IAC1D,CAAC;CACF;AAED;;;;GAIG;AACH,MAAM,OAAO,mBAAoB,SAAQ,UAAU;;AAC1C,6BAAS,GAAG,iBAAiB,CAAC;AAGvC;;;GAGG;AACH,MAAM,OAAO,kBAAmB,SAAQ,UAAU;;AACzC,4BAAS,GAAG,gBAAgB,CAAC;AAGtC;;;;GAIG;AACH,MAAM,OAAO,iBAAkB,SAAQ,UAAU;;AACxC,2BAAS,GAAG,eAAe,CAAC;AAGrC;;;GAGG;AACH,MAAM,OAAO,uBAAwB,SAAQ,UAAU;;AAC9C,iCAAS,GAAG,qBAAqB,CAAC;AAG3C;;;GAGG;AACH,MAAM,OAAO,yBAA0B,SAAQ,UAAU;;AAChD,mCAAS,GAAG,wBAAwB,CAAC;AAG9C;;;GAGG;AACH,MAAM,OAAO,iBAAkB,SAAQ,UAAU;;AACxC,2BAAS,GAAG,eAAe,CAAC;AAGrC;;GAEG;AACH,MAAM,OAAO,iBAAkB,SAAQ,UAAU;;AACxC,2BAAS,GAAG,eAAe,CAAC;AAGrC;;;GAGG;AACH,MAAM,OAAO,WAAY,SAAQ,UAAU;;AAClC,qBAAS,GAAG,cAAc,CAAC;AAGpC;;;GAGG;AACH,MAAM,OAAO,2BAA4B,SAAQ,UAAU;;AAClD,qCAAS,GAAG,yBAAyB,CAAC;AAG/C;;;GAGG;AACH,MAAM,OAAO,4BAA6B,SAAQ,UAAU;;AACnD,sCAAS,GAAG,2BAA2B,CAAC;AAGjD;;;GAGG;AACH,MAAM,OAAO,yBAA0B,SAAQ,UAAU;;AAChD,mCAAS,GAAG,wBAAwB,CAAC;AAG9C;;;GAGG;AACH,MAAM,OAAO,iBAAkB,SAAQ,UAAU;;AACxC,2BAAS,GAAG,eAAe,CAAC;AAGrC;;;GAGG;AACH,MAAM,OAAO,qBAAsB,SAAQ,UAAU;;AAC5C,+BAAS,GAAG,oBAAoB,CAAC;AAG1C;;;GAGG;AACH,MAAM,OAAO,oBAAqB,SAAQ,UAAU;;AAC3C,8BAAS,GAAG,mBAAmB,CAAC;AAGzC;;;GAGG;AACH,MAAM,OAAO,0BAA2B,SAAQ,UAAU;;AACjD,oCAAS,GAAG,yBAAyB,CAAC;AAG/C;;GAEG;AACH,MAAM,OAAO,sBAAuB,SAAQ,UAAU;;AAC7C,gCAAS,GAAG,oBAAoB,CAAC;AAG1C;;GAEG;AACH,MAAM,OAAO,gBAAiB,SAAQ,UAAU;IAC9C,YAA6B,eAAuB,EAAE,OAAe,EAAE,QAAiB;QACtF,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QADE,oBAAe,GAAf,eAAe,CAAQ;IAEpD,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;CACF;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,YAAY,GAAG;IAC1B,CAAC,mBAAmB,CAAC,SAAS,CAAC,EAAE,mBAAmB;IACpD,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,kBAAkB;IAClD,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,iBAAiB;IAChD,CAAC,uBAAuB,CAAC,SAAS,CAAC,EAAE,uBAAuB;IAC5D,CAAC,yBAAyB,CAAC,SAAS,CAAC,EAAE,yBAAyB;IAChE,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,iBAAiB;IAChD,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,iBAAiB;IAChD,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE,WAAW;IACpC,CAAC,2BAA2B,CAAC,SAAS,CAAC,EAAE,2BAA2B;IACpE,CAAC,4BAA4B,CAAC,SAAS,CAAC,EAAE,4BAA4B;IACtE,CAAC,yBAAyB,CAAC,SAAS,CAAC,EAAE,yBAAyB;IAChE,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,iBAAiB;IAChD,CAAC,qBAAqB,CAAC,SAAS,CAAC,EAAE,qBAAqB;IACxD,CAAC,oBAAoB,CAAC,SAAS,CAAC,EAAE,oBAAoB;IACtD,CAAC,0BAA0B,CAAC,SAAS,CAAC,EAAE,0BAA0B;IAClE,CAAC,sBAAsB,CAAC,SAAS,CAAC,EAAE,sBAAsB;CAClD,CAAC"}