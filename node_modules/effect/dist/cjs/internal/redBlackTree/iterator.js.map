{"version": 3, "file": "iterator.js", "names": ["Arr", "_interopRequireWildcard", "require", "Option", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "Direction", "exports", "Forward", "Backward", "RedBlackTreeIterator", "self", "stack", "direction", "count", "constructor", "clone", "slice", "reversed", "next", "entry", "moveNext", "movePrev", "_tag", "done", "value", "key", "length", "some", "none", "map", "last", "node", "index", "idx", "_root", "left", "s", "right", "push", "pop", "hasNext", "has<PERSON>rev"], "sources": ["../../../../src/internal/redBlackTree/iterator.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,GAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAF,uBAAA,CAAAC,OAAA;AAAyC,SAAAD,wBAAAG,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAL,uBAAA,YAAAA,CAAAG,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAKzC;AACO,MAAMkB,SAAS,GAAAC,OAAA,CAAAD,SAAA,GAAG;EACvBE,OAAO,EAAE,CAA+B;EACxCC,QAAQ,EAAE,CAAC,IAAI;CACP;AAEV;AACM,MAAOC,oBAAoB;EAIpBC,IAAA;EACAC,KAAA;EACAC,SAAA;EALHC,KAAK,GAAG,CAAC;EAEjBC,YACWJ,IAA4B,EAC5BC,KAA6B,EAC7BC,SAAqC;IAFrC,KAAAF,IAAI,GAAJA,IAAI;IACJ,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,SAAS,GAATA,SAAS;EACjB;EAEH;;;EAGAG,KAAKA,CAAA;IACH,OAAO,IAAIN,oBAAoB,CAAC,IAAI,CAACC,IAAI,EAAE,IAAI,CAACC,KAAK,CAACK,KAAK,EAAE,EAAE,IAAI,CAACJ,SAAS,CAAC;EAChF;EAEA;;;EAGAK,QAAQA,CAAA;IACN,OAAO,IAAIR,oBAAoB,CAC7B,IAAI,CAACC,IAAI,EACT,IAAI,CAACC,KAAK,CAACK,KAAK,EAAE,EAClB,IAAI,CAACJ,SAAS,KAAKP,SAAS,CAACE,OAAO,GAAGF,SAAS,CAACG,QAAQ,GAAGH,SAAS,CAACE,OAAO,CAC9E;EACH;EAEA;;;EAGAW,IAAIA,CAAA;IACF,MAAMC,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,IAAI,CAACN,KAAK,EAAE;IACZ,IAAI,IAAI,CAACD,SAAS,KAAKP,SAAS,CAACE,OAAO,EAAE;MACxC,IAAI,CAACa,QAAQ,EAAE;IACjB,CAAC,MAAM;MACL,IAAI,CAACC,QAAQ,EAAE;IACjB;IACA,QAAQF,KAAK,CAACG,IAAI;MAChB,KAAK,MAAM;QAAE;UACX,OAAO;YAAEC,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI,CAACX;UAAK,CAAE;QAC1C;MACA,KAAK,MAAM;QAAE;UACX,OAAO;YAAEU,IAAI,EAAE,KAAK;YAAEC,KAAK,EAAEL,KAAK,CAACK;UAAK,CAAE;QAC5C;IACF;EACF;EAEA;;;EAGA,IAAIC,GAAGA,CAAA;IACL,IAAI,IAAI,CAACd,KAAK,CAACe,MAAM,GAAG,CAAC,EAAE;MACzB,OAAOzC,MAAM,CAAC0C,IAAI,CAAC,IAAI,CAAChB,KAAK,CAAC,IAAI,CAACA,KAAK,CAACe,MAAM,GAAG,CAAC,CAAE,CAACD,GAAG,CAAC;IAC5D;IACA,OAAOxC,MAAM,CAAC2C,IAAI,EAAE;EACtB;EAEA;;;EAGA,IAAIJ,KAAKA,CAAA;IACP,IAAI,IAAI,CAACb,KAAK,CAACe,MAAM,GAAG,CAAC,EAAE;MACzB,OAAOzC,MAAM,CAAC0C,IAAI,CAAC,IAAI,CAAChB,KAAK,CAAC,IAAI,CAACA,KAAK,CAACe,MAAM,GAAG,CAAC,CAAE,CAACF,KAAK,CAAC;IAC9D;IACA,OAAOvC,MAAM,CAAC2C,IAAI,EAAE;EACtB;EAEA;;;EAGA,IAAIT,KAAKA,CAAA;IACP,OAAOlC,MAAM,CAAC4C,GAAG,CAAC/C,GAAG,CAACgD,IAAI,CAAC,IAAI,CAACnB,KAAK,CAAC,EAAGoB,IAAI,IAAK,CAACA,IAAI,CAACN,GAAG,EAAEM,IAAI,CAACP,KAAK,CAAC,CAAC;EAC3E;EAEA;;;EAGA,IAAIQ,KAAKA,CAAA;IACP,IAAIC,GAAG,GAAG,CAAC;IACX,MAAMtB,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,IAAIA,KAAK,CAACe,MAAM,KAAK,CAAC,EAAE;MACtB,MAAMrC,CAAC,GAAI,IAAI,CAACqB,IAA+B,CAACwB,KAAK;MACrD,IAAI7C,CAAC,IAAI,IAAI,EAAE;QACb,OAAOA,CAAC,CAACwB,KAAK;MAChB;MACA,OAAO,CAAC;IACV,CAAC,MAAM,IAAIF,KAAK,CAACA,KAAK,CAACe,MAAM,GAAG,CAAC,CAAE,CAACS,IAAI,IAAI,IAAI,EAAE;MAChDF,GAAG,GAAGtB,KAAK,CAACA,KAAK,CAACe,MAAM,GAAG,CAAC,CAAE,CAACS,IAAK,CAACtB,KAAK;IAC5C;IACA,KAAK,IAAIuB,CAAC,GAAGzB,KAAK,CAACe,MAAM,GAAG,CAAC,EAAEU,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;MAC1C,IAAIzB,KAAK,CAACyB,CAAC,GAAG,CAAC,CAAC,KAAKzB,KAAK,CAACyB,CAAC,CAAE,CAACC,KAAK,EAAE;QACpC,EAAEJ,GAAG;QACL,IAAItB,KAAK,CAACyB,CAAC,CAAE,CAACD,IAAI,IAAI,IAAI,EAAE;UAC1BF,GAAG,IAAItB,KAAK,CAACyB,CAAC,CAAE,CAACD,IAAK,CAACtB,KAAK;QAC9B;MACF;IACF;IACA,OAAOoB,GAAG;EACZ;EAEA;;;EAGAb,QAAQA,CAAA;IACN,MAAMT,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,IAAIA,KAAK,CAACe,MAAM,KAAK,CAAC,EAAE;MACtB;IACF;IACA,IAAIpC,CAAC,GAAgCqB,KAAK,CAACA,KAAK,CAACe,MAAM,GAAG,CAAC,CAAE;IAC7D,IAAIpC,CAAC,CAAC+C,KAAK,IAAI,IAAI,EAAE;MACnB/C,CAAC,GAAGA,CAAC,CAAC+C,KAAK;MACX,OAAO/C,CAAC,IAAI,IAAI,EAAE;QAChBqB,KAAK,CAAC2B,IAAI,CAAChD,CAAC,CAAC;QACbA,CAAC,GAAGA,CAAC,CAAC6C,IAAI;MACZ;IACF,CAAC,MAAM;MACLxB,KAAK,CAAC4B,GAAG,EAAE;MACX,OAAO5B,KAAK,CAACe,MAAM,GAAG,CAAC,IAAIf,KAAK,CAACA,KAAK,CAACe,MAAM,GAAG,CAAC,CAAE,CAACW,KAAK,KAAK/C,CAAC,EAAE;QAC/DA,CAAC,GAAGqB,KAAK,CAACA,KAAK,CAACe,MAAM,GAAG,CAAC,CAAC;QAC3Bf,KAAK,CAAC4B,GAAG,EAAE;MACb;IACF;EACF;EAEA;;;EAGA,IAAIC,OAAOA,CAAA;IACT,MAAM7B,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,IAAIA,KAAK,CAACe,MAAM,KAAK,CAAC,EAAE;MACtB,OAAO,KAAK;IACd;IACA,IAAIf,KAAK,CAACA,KAAK,CAACe,MAAM,GAAG,CAAC,CAAE,CAACW,KAAK,IAAI,IAAI,EAAE;MAC1C,OAAO,IAAI;IACb;IACA,KAAK,IAAID,CAAC,GAAGzB,KAAK,CAACe,MAAM,GAAG,CAAC,EAAEU,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;MACzC,IAAIzB,KAAK,CAACyB,CAAC,GAAG,CAAC,CAAE,CAACD,IAAI,KAAKxB,KAAK,CAACyB,CAAC,CAAC,EAAE;QACnC,OAAO,IAAI;MACb;IACF;IACA,OAAO,KAAK;EACd;EAEA;;;EAGAf,QAAQA,CAAA;IACN,MAAMV,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,IAAIA,KAAK,CAACe,MAAM,KAAK,CAAC,EAAE;MACtB;IACF;IACA,IAAIpC,CAAC,GAAgCqB,KAAK,CAACA,KAAK,CAACe,MAAM,GAAG,CAAC,CAAC;IAC5D,IAAIpC,CAAC,IAAI,IAAI,IAAIA,CAAC,CAAC6C,IAAI,IAAI,IAAI,EAAE;MAC/B7C,CAAC,GAAGA,CAAC,CAAC6C,IAAI;MACV,OAAO7C,CAAC,IAAI,IAAI,EAAE;QAChBqB,KAAK,CAAC2B,IAAI,CAAChD,CAAC,CAAC;QACbA,CAAC,GAAGA,CAAC,CAAC+C,KAAK;MACb;IACF,CAAC,MAAM;MACL1B,KAAK,CAAC4B,GAAG,EAAE;MACX,OAAO5B,KAAK,CAACe,MAAM,GAAG,CAAC,IAAIf,KAAK,CAACA,KAAK,CAACe,MAAM,GAAG,CAAC,CAAE,CAACS,IAAI,KAAK7C,CAAC,EAAE;QAC9DA,CAAC,GAAGqB,KAAK,CAACA,KAAK,CAACe,MAAM,GAAG,CAAC,CAAC;QAC3Bf,KAAK,CAAC4B,GAAG,EAAE;MACb;IACF;EACF;EAEA;;;EAGA,IAAIE,OAAOA,CAAA;IACT,MAAM9B,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,IAAIA,KAAK,CAACe,MAAM,KAAK,CAAC,EAAE;MACtB,OAAO,KAAK;IACd;IACA,IAAIf,KAAK,CAACA,KAAK,CAACe,MAAM,GAAG,CAAC,CAAE,CAACS,IAAI,IAAI,IAAI,EAAE;MACzC,OAAO,IAAI;IACb;IACA,KAAK,IAAIC,CAAC,GAAGzB,KAAK,CAACe,MAAM,GAAG,CAAC,EAAEU,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;MACzC,IAAIzB,KAAK,CAACyB,CAAC,GAAG,CAAC,CAAE,CAACC,KAAK,KAAK1B,KAAK,CAACyB,CAAC,CAAC,EAAE;QACpC,OAAO,IAAI;MACb;IACF;IACA,OAAO,KAAK;EACd", "ignoreList": []}