{"version": 3, "file": "node.js", "names": ["equals", "O", "isTagged", "<PERSON><PERSON>", "arraySpliceIn", "arraySpliceOut", "arrayUpdate", "fromBitmap", "hashFragment", "toBitmap", "MAX_INDEX_NODE", "MIN_ARRAY_NODE", "SIZE", "EmptyNode", "_tag", "modify", "edit", "_shift", "f", "hash", "key", "size", "v", "none", "isNone", "value", "LeafNode", "isEmptyNode", "a", "isLeafNode", "node", "canEditNode", "constructor", "shift", "mergeLeaves", "CollisionNode", "children", "canEdit", "list", "updateCollisionList", "length", "mutate", "len", "i", "child", "newValue", "IndexedNode", "mask", "frag", "bit", "indx", "exists", "_new<PERSON><PERSON>d", "expand", "current", "bitmap", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ArrayNode", "count", "<PERSON><PERSON><PERSON><PERSON>", "pack", "removed", "elements", "Array", "g", "elem", "subNodes", "arr", "mergeLeavesInner", "h1", "n1", "h2", "n2", "subH1", "subH2", "stack", "undefined", "currentShift", "res", "make", "final", "previous"], "sources": ["../../../../src/internal/hashMap/node.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,MAAM,QAAQ,gBAAgB;AAEvC,OAAO,KAAKC,CAAC,MAAM,iBAAiB;AACpC,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,OAAO,KAAKC,KAAK,MAAM,aAAa;AACpC,SAASC,aAAa,EAAEC,cAAc,EAAEC,WAAW,QAAQ,YAAY;AACvE,SAASC,UAAU,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,cAAc;AACjE,SAASC,cAAc,EAAEC,cAAc,EAAEC,IAAI,QAAQ,aAAa;AAelE;AACA,OAAM,MAAOC,SAAS;EACXC,IAAI,GAAG,WAAW;EAE3BC,MAAMA,CACJC,IAAY,EACZC,MAAc,EACdC,CAAsB,EACtBC,IAAY,EACZC,GAAM,EACNC,IAAa;IAEb,MAAMC,CAAC,GAAGJ,CAAC,CAACjB,CAAC,CAACsB,IAAI,EAAE,CAAC;IACrB,IAAItB,CAAC,CAACuB,MAAM,CAACF,CAAC,CAAC,EAAE,OAAO,IAAIT,SAAS,EAAE;IACvC,EAAEQ,IAAI,CAACI,KAAK;IACZ,OAAO,IAAIC,QAAQ,CAACV,IAAI,EAAEG,IAAI,EAAEC,GAAG,EAAEE,CAAC,CAAC;EACzC;;AAGF;AACA,OAAM,SAAUK,WAAWA,CAACC,CAAU;EACpC,OAAO1B,QAAQ,CAAC0B,CAAC,EAAE,WAAW,CAAC;AACjC;AAEA;AACA,OAAM,SAAUC,UAAUA,CACxBC,IAAgB;EAEhB,OAAOH,WAAW,CAACG,IAAI,CAAC,IAAIA,IAAI,CAAChB,IAAI,KAAK,UAAU,IAAIgB,IAAI,CAAChB,IAAI,KAAK,eAAe;AACvF;AAEA;AACA,OAAM,SAAUiB,WAAWA,CAAOD,IAAgB,EAAEd,IAAY;EAC9D,OAAOW,WAAW,CAACG,IAAI,CAAC,GAAG,KAAK,GAAGd,IAAI,KAAKc,IAAI,CAACd,IAAI;AACvD;AAEA;AACA,OAAM,MAAOU,QAAQ;EAIRV,IAAA;EACAG,IAAA;EACAC,GAAA;EACFK,KAAA;EANAX,IAAI,GAAG,UAAU;EAE1BkB,YACWhB,IAAY,EACZG,IAAY,EACZC,GAAM,EACRK,KAAkB;IAHhB,KAAAT,IAAI,GAAJA,IAAI;IACJ,KAAAG,IAAI,GAAJA,IAAI;IACJ,KAAAC,GAAG,GAAHA,GAAG;IACL,KAAAK,KAAK,GAALA,KAAK;EACX;EAEHV,MAAMA,CACJC,IAAY,EACZiB,KAAa,EACbf,CAAsB,EACtBC,IAAY,EACZC,GAAM,EACNC,IAAa;IAEb,IAAIrB,MAAM,CAACoB,GAAG,EAAE,IAAI,CAACA,GAAG,CAAC,EAAE;MACzB,MAAME,CAAC,GAAGJ,CAAC,CAAC,IAAI,CAACO,KAAK,CAAC;MACvB,IAAIH,CAAC,KAAK,IAAI,CAACG,KAAK,EAAE,OAAO,IAAI,MAC5B,IAAIxB,CAAC,CAACuB,MAAM,CAACF,CAAC,CAAC,EAAE;QACpB,EAAED,IAAI,CAACI,KAAK;QACZ,OAAO,IAAIZ,SAAS,EAAE;MACxB;MACA,IAAIkB,WAAW,CAAC,IAAI,EAAEf,IAAI,CAAC,EAAE;QAC3B,IAAI,CAACS,KAAK,GAAGH,CAAC;QACd,OAAO,IAAI;MACb;MACA,OAAO,IAAII,QAAQ,CAACV,IAAI,EAAEG,IAAI,EAAEC,GAAG,EAAEE,CAAC,CAAC;IACzC;IACA,MAAMA,CAAC,GAAGJ,CAAC,CAACjB,CAAC,CAACsB,IAAI,EAAE,CAAC;IACrB,IAAItB,CAAC,CAACuB,MAAM,CAACF,CAAC,CAAC,EAAE,OAAO,IAAI;IAC5B,EAAED,IAAI,CAACI,KAAK;IACZ,OAAOS,WAAW,CAChBlB,IAAI,EACJiB,KAAK,EACL,IAAI,CAACd,IAAI,EACT,IAAI,EACJA,IAAI,EACJ,IAAIO,QAAQ,CAACV,IAAI,EAAEG,IAAI,EAAEC,GAAG,EAAEE,CAAC,CAAC,CACjC;EACH;;AAGF;AACA,OAAM,MAAOa,aAAa;EAIbnB,IAAA;EACAG,IAAA;EACAiB,QAAA;EALFtB,IAAI,GAAG,eAAe;EAE/BkB,YACWhB,IAAY,EACZG,IAAY,EACZiB,QAA2B;IAF3B,KAAApB,IAAI,GAAJA,IAAI;IACJ,KAAAG,IAAI,GAAJA,IAAI;IACJ,KAAAiB,QAAQ,GAARA,QAAQ;EAChB;EAEHrB,MAAMA,CACJC,IAAY,EACZiB,KAAa,EACbf,CAAsB,EACtBC,IAAY,EACZC,GAAM,EACNC,IAAa;IAEb,IAAIF,IAAI,KAAK,IAAI,CAACA,IAAI,EAAE;MACtB,MAAMkB,OAAO,GAAGN,WAAW,CAAC,IAAI,EAAEf,IAAI,CAAC;MACvC,MAAMsB,IAAI,GAAG,IAAI,CAACC,mBAAmB,CACnCF,OAAO,EACPrB,IAAI,EACJ,IAAI,CAACG,IAAI,EACT,IAAI,CAACiB,QAAQ,EACblB,CAAC,EACDE,GAAG,EACHC,IAAI,CACL;MACD,IAAIiB,IAAI,KAAK,IAAI,CAACF,QAAQ,EAAE,OAAO,IAAI;MAEvC,OAAOE,IAAI,CAACE,MAAM,GAAG,CAAC,GAAG,IAAIL,aAAa,CAACnB,IAAI,EAAE,IAAI,CAACG,IAAI,EAAEmB,IAAI,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAE,EAAC;IAC/E;IACA,MAAMhB,CAAC,GAAGJ,CAAC,CAACjB,CAAC,CAACsB,IAAI,EAAE,CAAC;IACrB,IAAItB,CAAC,CAACuB,MAAM,CAACF,CAAC,CAAC,EAAE,OAAO,IAAI;IAC5B,EAAED,IAAI,CAACI,KAAK;IACZ,OAAOS,WAAW,CAChBlB,IAAI,EACJiB,KAAK,EACL,IAAI,CAACd,IAAI,EACT,IAAI,EACJA,IAAI,EACJ,IAAIO,QAAQ,CAACV,IAAI,EAAEG,IAAI,EAAEC,GAAG,EAAEE,CAAC,CAAC,CACjC;EACH;EAEAiB,mBAAmBA,CACjBE,MAAe,EACfzB,IAAY,EACZG,IAAY,EACZmB,IAAuB,EACvBpB,CAAsB,EACtBE,GAAM,EACNC,IAAa;IAEb,MAAMqB,GAAG,GAAGJ,IAAI,CAACE,MAAM;IACvB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,GAAG,EAAE,EAAEC,CAAC,EAAE;MAC5B,MAAMC,KAAK,GAAGN,IAAI,CAACK,CAAC,CAAE;MACtB,IAAI,KAAK,IAAIC,KAAK,IAAI5C,MAAM,CAACoB,GAAG,EAAEwB,KAAK,CAACxB,GAAG,CAAC,EAAE;QAC5C,MAAMK,KAAK,GAAGmB,KAAK,CAACnB,KAAK;QACzB,MAAMoB,QAAQ,GAAG3B,CAAC,CAACO,KAAK,CAAC;QACzB,IAAIoB,QAAQ,KAAKpB,KAAK,EAAE,OAAOa,IAAI;QACnC,IAAIrC,CAAC,CAACuB,MAAM,CAACqB,QAAQ,CAAC,EAAE;UACtB,EAAExB,IAAI,CAACI,KAAK;UACZ,OAAOpB,cAAc,CAACoC,MAAM,EAAEE,CAAC,EAAEL,IAAI,CAAC;QACxC;QACA,OAAOhC,WAAW,CAACmC,MAAM,EAAEE,CAAC,EAAE,IAAIjB,QAAQ,CAACV,IAAI,EAAEG,IAAI,EAAEC,GAAG,EAAEyB,QAAQ,CAAC,EAAEP,IAAI,CAAC;MAC9E;IACF;IAEA,MAAMO,QAAQ,GAAG3B,CAAC,CAACjB,CAAC,CAACsB,IAAI,EAAE,CAAC;IAC5B,IAAItB,CAAC,CAACuB,MAAM,CAACqB,QAAQ,CAAC,EAAE,OAAOP,IAAI;IACnC,EAAEjB,IAAI,CAACI,KAAK;IACZ,OAAOnB,WAAW,CAACmC,MAAM,EAAEC,GAAG,EAAE,IAAIhB,QAAQ,CAACV,IAAI,EAAEG,IAAI,EAAEC,GAAG,EAAEyB,QAAQ,CAAC,EAAEP,IAAI,CAAC;EAChF;;AAGF;AACA,OAAM,MAAOQ,WAAW;EAIX9B,IAAA;EACF+B,IAAA;EACAX,QAAA;EALAtB,IAAI,GAAG,aAAa;EAE7BkB,YACWhB,IAAY,EACd+B,IAAY,EACZX,QAA2B;IAFzB,KAAApB,IAAI,GAAJA,IAAI;IACN,KAAA+B,IAAI,GAAJA,IAAI;IACJ,KAAAX,QAAQ,GAARA,QAAQ;EACd;EAEHrB,MAAMA,CACJC,IAAY,EACZiB,KAAa,EACbf,CAAsB,EACtBC,IAAY,EACZC,GAAM,EACNC,IAAa;IAEb,MAAM0B,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMX,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,MAAMY,IAAI,GAAGxC,YAAY,CAACyB,KAAK,EAAEd,IAAI,CAAC;IACtC,MAAM8B,GAAG,GAAGxC,QAAQ,CAACuC,IAAI,CAAC;IAC1B,MAAME,IAAI,GAAG3C,UAAU,CAACwC,IAAI,EAAEE,GAAG,CAAC;IAClC,MAAME,MAAM,GAAGJ,IAAI,GAAGE,GAAG;IACzB,MAAMZ,OAAO,GAAGN,WAAW,CAAC,IAAI,EAAEf,IAAI,CAAC;IAEvC,IAAI,CAACmC,MAAM,EAAE;MACX,MAAMC,SAAS,GAAG,IAAIvC,SAAS,EAAQ,CAACE,MAAM,CAACC,IAAI,EAAEiB,KAAK,GAAGrB,IAAI,EAAEM,CAAC,EAAEC,IAAI,EAAEC,GAAG,EAAEC,IAAI,CAAC;MACtF,IAAI,CAAC+B,SAAS,EAAE,OAAO,IAAI;MAC3B,OAAOhB,QAAQ,CAACI,MAAM,IAAI9B,cAAc,GACtC2C,MAAM,CAACrC,IAAI,EAAEgC,IAAI,EAAEI,SAAS,EAAEL,IAAI,EAAEX,QAAQ,CAAC,GAC7C,IAAIU,WAAW,CAAC9B,IAAI,EAAE+B,IAAI,GAAGE,GAAG,EAAE7C,aAAa,CAACiC,OAAO,EAAEa,IAAI,EAAEE,SAAS,EAAEhB,QAAQ,CAAC,CAAC;IACxF;IAEA,MAAMkB,OAAO,GAAGlB,QAAQ,CAACc,IAAI,CAAE;IAC/B,MAAMN,KAAK,GAAGU,OAAO,CAACvC,MAAM,CAACC,IAAI,EAAEiB,KAAK,GAAGrB,IAAI,EAAEM,CAAC,EAAEC,IAAI,EAAEC,GAAG,EAAEC,IAAI,CAAC;IAEpE,IAAIiC,OAAO,KAAKV,KAAK,EAAE,OAAO,IAAI;IAClC,IAAIW,MAAM,GAAGR,IAAI;IACjB,IAAIS,WAAW;IACf,IAAI7B,WAAW,CAACiB,KAAK,CAAC,EAAE;MACtB;MACAW,MAAM,IAAI,CAACN,GAAG;MACd,IAAI,CAACM,MAAM,EAAE,OAAO,IAAI1C,SAAS,EAAE;MACnC,IAAIuB,QAAQ,CAACI,MAAM,IAAI,CAAC,IAAIX,UAAU,CAACO,QAAQ,CAACc,IAAI,GAAG,CAAC,CAAE,CAAC,EAAE;QAC3D,OAAOd,QAAQ,CAACc,IAAI,GAAG,CAAC,CAAE,EAAC;MAC7B;MAEAM,WAAW,GAAGnD,cAAc,CAACgC,OAAO,EAAEa,IAAI,EAAEd,QAAQ,CAAC;IACvD,CAAC,MAAM;MACL;MACAoB,WAAW,GAAGlD,WAAW,CAAC+B,OAAO,EAAEa,IAAI,EAAEN,KAAK,EAAER,QAAQ,CAAC;IAC3D;IAEA,IAAIC,OAAO,EAAE;MACX,IAAI,CAACU,IAAI,GAAGQ,MAAM;MAClB,IAAI,CAACnB,QAAQ,GAAGoB,WAAW;MAC3B,OAAO,IAAI;IACb;IAEA,OAAO,IAAIV,WAAW,CAAC9B,IAAI,EAAEuC,MAAM,EAAEC,WAAW,CAAC;EACnD;;AAGF;AACA,OAAM,MAAOC,SAAS;EAITzC,IAAA;EACFK,IAAA;EACAe,QAAA;EALAtB,IAAI,GAAG,WAAW;EAE3BkB,YACWhB,IAAY,EACdK,IAAY,EACZe,QAA2B;IAFzB,KAAApB,IAAI,GAAJA,IAAI;IACN,KAAAK,IAAI,GAAJA,IAAI;IACJ,KAAAe,QAAQ,GAARA,QAAQ;EACd;EAEHrB,MAAMA,CACJC,IAAY,EACZiB,KAAa,EACbf,CAAsB,EACtBC,IAAY,EACZC,GAAM,EACNC,IAAa;IAEb,IAAIqC,KAAK,GAAG,IAAI,CAACrC,IAAI;IACrB,MAAMe,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,MAAMY,IAAI,GAAGxC,YAAY,CAACyB,KAAK,EAAEd,IAAI,CAAC;IACtC,MAAMyB,KAAK,GAAGR,QAAQ,CAACY,IAAI,CAAC;IAC5B,MAAMW,QAAQ,GAAG,CAACf,KAAK,IAAI,IAAI/B,SAAS,EAAQ,EAAEE,MAAM,CACtDC,IAAI,EACJiB,KAAK,GAAGrB,IAAI,EACZM,CAAC,EACDC,IAAI,EACJC,GAAG,EACHC,IAAI,CACL;IAED,IAAIuB,KAAK,KAAKe,QAAQ,EAAE,OAAO,IAAI;IAEnC,MAAMtB,OAAO,GAAGN,WAAW,CAAC,IAAI,EAAEf,IAAI,CAAC;IACvC,IAAIwC,WAAW;IACf,IAAI7B,WAAW,CAACiB,KAAK,CAAC,IAAI,CAACjB,WAAW,CAACgC,QAAQ,CAAC,EAAE;MAChD;MACA,EAAED,KAAK;MACPF,WAAW,GAAGlD,WAAW,CAAC+B,OAAO,EAAEW,IAAI,EAAEW,QAAQ,EAAEvB,QAAQ,CAAC;IAC9D,CAAC,MAAM,IAAI,CAACT,WAAW,CAACiB,KAAK,CAAC,IAAIjB,WAAW,CAACgC,QAAQ,CAAC,EAAE;MACvD;MACA,EAAED,KAAK;MACP,IAAIA,KAAK,IAAI/C,cAAc,EAAE;QAC3B,OAAOiD,IAAI,CAAC5C,IAAI,EAAE0C,KAAK,EAAEV,IAAI,EAAEZ,QAAQ,CAAC;MAC1C;MACAoB,WAAW,GAAGlD,WAAW,CAAC+B,OAAO,EAAEW,IAAI,EAAE,IAAInC,SAAS,EAAQ,EAAEuB,QAAQ,CAAC;IAC3E,CAAC,MAAM;MACL;MACAoB,WAAW,GAAGlD,WAAW,CAAC+B,OAAO,EAAEW,IAAI,EAAEW,QAAQ,EAAEvB,QAAQ,CAAC;IAC9D;IAEA,IAAIC,OAAO,EAAE;MACX,IAAI,CAAChB,IAAI,GAAGqC,KAAK;MACjB,IAAI,CAACtB,QAAQ,GAAGoB,WAAW;MAC3B,OAAO,IAAI;IACb;IACA,OAAO,IAAIC,SAAS,CAACzC,IAAI,EAAE0C,KAAK,EAAEF,WAAW,CAAC;EAChD;;AAGF,SAASI,IAAIA,CACX5C,IAAY,EACZ0C,KAAa,EACbG,OAAe,EACfC,QAA2B;EAE3B,MAAM1B,QAAQ,GAAG,IAAI2B,KAAK,CAAaL,KAAK,GAAG,CAAC,CAAC;EACjD,IAAIM,CAAC,GAAG,CAAC;EACT,IAAIT,MAAM,GAAG,CAAC;EACd,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAED,GAAG,GAAGoB,QAAQ,CAACtB,MAAM,EAAEG,CAAC,GAAGD,GAAG,EAAE,EAAEC,CAAC,EAAE;IACnD,IAAIA,CAAC,KAAKkB,OAAO,EAAE;MACjB,MAAMI,IAAI,GAAGH,QAAQ,CAACnB,CAAC,CAAC;MACxB,IAAIsB,IAAI,IAAI,CAACtC,WAAW,CAACsC,IAAI,CAAC,EAAE;QAC9B7B,QAAQ,CAAC4B,CAAC,EAAE,CAAC,GAAGC,IAAI;QACpBV,MAAM,IAAI,CAAC,IAAIZ,CAAC;MAClB;IACF;EACF;EACA,OAAO,IAAIG,WAAW,CAAC9B,IAAI,EAAEuC,MAAM,EAAEnB,QAAQ,CAAC;AAChD;AAEA,SAASiB,MAAMA,CACbrC,IAAY,EACZgC,IAAY,EACZJ,KAAiB,EACjBW,MAAc,EACdW,QAA2B;EAE3B,MAAMC,GAAG,GAAG,EAAE;EACd,IAAIlB,GAAG,GAAGM,MAAM;EAChB,IAAIG,KAAK,GAAG,CAAC;EACb,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEM,GAAG,EAAE,EAAEN,CAAC,EAAE;IACxB,IAAIM,GAAG,GAAG,CAAC,EAAEkB,GAAG,CAACxB,CAAC,CAAC,GAAGuB,QAAQ,CAACR,KAAK,EAAE,CAAE;IACxCT,GAAG,MAAM,CAAC;EACZ;EACAkB,GAAG,CAACnB,IAAI,CAAC,GAAGJ,KAAK;EACjB,OAAO,IAAIa,SAAS,CAACzC,IAAI,EAAE0C,KAAK,GAAG,CAAC,EAAES,GAAG,CAAC;AAC5C;AAEA,SAASC,gBAAgBA,CACvBpD,IAAY,EACZiB,KAAa,EACboC,EAAU,EACVC,EAAc,EACdC,EAAU,EACVC,EAAc;EAEd,IAAIH,EAAE,KAAKE,EAAE,EAAE,OAAO,IAAIpC,aAAa,CAACnB,IAAI,EAAEqD,EAAE,EAAE,CAACG,EAAE,EAAEF,EAAE,CAAC,CAAC;EAC3D,MAAMG,KAAK,GAAGjE,YAAY,CAACyB,KAAK,EAAEoC,EAAE,CAAC;EACrC,MAAMK,KAAK,GAAGlE,YAAY,CAACyB,KAAK,EAAEsC,EAAE,CAAC;EAErC,IAAIE,KAAK,KAAKC,KAAK,EAAE;IACnB,OAAQ9B,KAAK,IAAK,IAAIE,WAAW,CAAC9B,IAAI,EAAEP,QAAQ,CAACgE,KAAK,CAAC,GAAGhE,QAAQ,CAACiE,KAAK,CAAC,EAAE,CAAC9B,KAAK,CAAC,CAAC;EACrF,CAAC,MAAM;IACL,MAAMR,QAAQ,GAAGqC,KAAK,GAAGC,KAAK,GAAG,CAACJ,EAAE,EAAEE,EAAE,CAAC,GAAG,CAACA,EAAE,EAAEF,EAAE,CAAC;IACpD,OAAO,IAAIxB,WAAW,CAAC9B,IAAI,EAAEP,QAAQ,CAACgE,KAAK,CAAC,GAAGhE,QAAQ,CAACiE,KAAK,CAAC,EAAEtC,QAAQ,CAAC;EAC3E;AACF;AAEA,SAASF,WAAWA,CAClBlB,IAAY,EACZiB,KAAa,EACboC,EAAU,EACVC,EAAc,EACdC,EAAU,EACVC,EAAc;EAEd,IAAIG,KAAK,GAA8DC,SAAS;EAChF,IAAIC,YAAY,GAAG5C,KAAK;EAExB,OAAO,IAAI,EAAE;IACX,MAAM6C,GAAG,GAAGV,gBAAgB,CAACpD,IAAI,EAAE6D,YAAY,EAAER,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;IAEhE,IAAI,OAAOM,GAAG,KAAK,UAAU,EAAE;MAC7BH,KAAK,GAAGxE,KAAK,CAAC4E,IAAI,CAACD,GAAG,EAAEH,KAAK,CAAC;MAC9BE,YAAY,GAAGA,YAAY,GAAGjE,IAAI;IACpC,CAAC,MAAM;MACL,IAAIoE,KAAK,GAAGF,GAAG;MACf,OAAOH,KAAK,IAAI,IAAI,EAAE;QACpBK,KAAK,GAAGL,KAAK,CAAClD,KAAK,CAACuD,KAAK,CAAC;QAC1BL,KAAK,GAAGA,KAAK,CAACM,QAAQ;MACxB;MACA,OAAOD,KAAK;IACd;EACF;AACF", "ignoreList": []}