{"version": 3, "file": "iterator.js", "names": ["Arr", "Option", "Direction", "Forward", "Backward", "RedBlackTreeIterator", "self", "stack", "direction", "count", "constructor", "clone", "slice", "reversed", "next", "entry", "moveNext", "movePrev", "_tag", "done", "value", "key", "length", "some", "none", "map", "last", "node", "index", "idx", "r", "_root", "left", "s", "right", "n", "push", "pop", "hasNext", "has<PERSON>rev"], "sources": ["../../../../src/internal/redBlackTree/iterator.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,GAAG,MAAM,gBAAgB;AACrC,OAAO,KAAKC,MAAM,MAAM,iBAAiB;AAKzC;AACA,OAAO,MAAMC,SAAS,GAAG;EACvBC,OAAO,EAAE,CAA+B;EACxCC,QAAQ,EAAE,CAAC,IAAI;CACP;AAEV;AACA,OAAM,MAAOC,oBAAoB;EAIpBC,IAAA;EACAC,KAAA;EACAC,SAAA;EALHC,KAAK,GAAG,CAAC;EAEjBC,YACWJ,IAA4B,EAC5BC,KAA6B,EAC7BC,SAAqC;IAFrC,KAAAF,IAAI,GAAJA,IAAI;IACJ,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,SAAS,GAATA,SAAS;EACjB;EAEH;;;EAGAG,KAAKA,CAAA;IACH,OAAO,IAAIN,oBAAoB,CAAC,IAAI,CAACC,IAAI,EAAE,IAAI,CAACC,KAAK,CAACK,KAAK,EAAE,EAAE,IAAI,CAACJ,SAAS,CAAC;EAChF;EAEA;;;EAGAK,QAAQA,CAAA;IACN,OAAO,IAAIR,oBAAoB,CAC7B,IAAI,CAACC,IAAI,EACT,IAAI,CAACC,KAAK,CAACK,KAAK,EAAE,EAClB,IAAI,CAACJ,SAAS,KAAKN,SAAS,CAACC,OAAO,GAAGD,SAAS,CAACE,QAAQ,GAAGF,SAAS,CAACC,OAAO,CAC9E;EACH;EAEA;;;EAGAW,IAAIA,CAAA;IACF,MAAMC,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,IAAI,CAACN,KAAK,EAAE;IACZ,IAAI,IAAI,CAACD,SAAS,KAAKN,SAAS,CAACC,OAAO,EAAE;MACxC,IAAI,CAACa,QAAQ,EAAE;IACjB,CAAC,MAAM;MACL,IAAI,CAACC,QAAQ,EAAE;IACjB;IACA,QAAQF,KAAK,CAACG,IAAI;MAChB,KAAK,MAAM;QAAE;UACX,OAAO;YAAEC,IAAI,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI,CAACX;UAAK,CAAE;QAC1C;MACA,KAAK,MAAM;QAAE;UACX,OAAO;YAAEU,IAAI,EAAE,KAAK;YAAEC,KAAK,EAAEL,KAAK,CAACK;UAAK,CAAE;QAC5C;IACF;EACF;EAEA;;;EAGA,IAAIC,GAAGA,CAAA;IACL,IAAI,IAAI,CAACd,KAAK,CAACe,MAAM,GAAG,CAAC,EAAE;MACzB,OAAOrB,MAAM,CAACsB,IAAI,CAAC,IAAI,CAAChB,KAAK,CAAC,IAAI,CAACA,KAAK,CAACe,MAAM,GAAG,CAAC,CAAE,CAACD,GAAG,CAAC;IAC5D;IACA,OAAOpB,MAAM,CAACuB,IAAI,EAAE;EACtB;EAEA;;;EAGA,IAAIJ,KAAKA,CAAA;IACP,IAAI,IAAI,CAACb,KAAK,CAACe,MAAM,GAAG,CAAC,EAAE;MACzB,OAAOrB,MAAM,CAACsB,IAAI,CAAC,IAAI,CAAChB,KAAK,CAAC,IAAI,CAACA,KAAK,CAACe,MAAM,GAAG,CAAC,CAAE,CAACF,KAAK,CAAC;IAC9D;IACA,OAAOnB,MAAM,CAACuB,IAAI,EAAE;EACtB;EAEA;;;EAGA,IAAIT,KAAKA,CAAA;IACP,OAAOd,MAAM,CAACwB,GAAG,CAACzB,GAAG,CAAC0B,IAAI,CAAC,IAAI,CAACnB,KAAK,CAAC,EAAGoB,IAAI,IAAK,CAACA,IAAI,CAACN,GAAG,EAAEM,IAAI,CAACP,KAAK,CAAC,CAAC;EAC3E;EAEA;;;EAGA,IAAIQ,KAAKA,CAAA;IACP,IAAIC,GAAG,GAAG,CAAC;IACX,MAAMtB,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,IAAIA,KAAK,CAACe,MAAM,KAAK,CAAC,EAAE;MACtB,MAAMQ,CAAC,GAAI,IAAI,CAACxB,IAA+B,CAACyB,KAAK;MACrD,IAAID,CAAC,IAAI,IAAI,EAAE;QACb,OAAOA,CAAC,CAACrB,KAAK;MAChB;MACA,OAAO,CAAC;IACV,CAAC,MAAM,IAAIF,KAAK,CAACA,KAAK,CAACe,MAAM,GAAG,CAAC,CAAE,CAACU,IAAI,IAAI,IAAI,EAAE;MAChDH,GAAG,GAAGtB,KAAK,CAACA,KAAK,CAACe,MAAM,GAAG,CAAC,CAAE,CAACU,IAAK,CAACvB,KAAK;IAC5C;IACA,KAAK,IAAIwB,CAAC,GAAG1B,KAAK,CAACe,MAAM,GAAG,CAAC,EAAEW,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;MAC1C,IAAI1B,KAAK,CAAC0B,CAAC,GAAG,CAAC,CAAC,KAAK1B,KAAK,CAAC0B,CAAC,CAAE,CAACC,KAAK,EAAE;QACpC,EAAEL,GAAG;QACL,IAAItB,KAAK,CAAC0B,CAAC,CAAE,CAACD,IAAI,IAAI,IAAI,EAAE;UAC1BH,GAAG,IAAItB,KAAK,CAAC0B,CAAC,CAAE,CAACD,IAAK,CAACvB,KAAK;QAC9B;MACF;IACF;IACA,OAAOoB,GAAG;EACZ;EAEA;;;EAGAb,QAAQA,CAAA;IACN,MAAMT,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,IAAIA,KAAK,CAACe,MAAM,KAAK,CAAC,EAAE;MACtB;IACF;IACA,IAAIa,CAAC,GAAgC5B,KAAK,CAACA,KAAK,CAACe,MAAM,GAAG,CAAC,CAAE;IAC7D,IAAIa,CAAC,CAACD,KAAK,IAAI,IAAI,EAAE;MACnBC,CAAC,GAAGA,CAAC,CAACD,KAAK;MACX,OAAOC,CAAC,IAAI,IAAI,EAAE;QAChB5B,KAAK,CAAC6B,IAAI,CAACD,CAAC,CAAC;QACbA,CAAC,GAAGA,CAAC,CAACH,IAAI;MACZ;IACF,CAAC,MAAM;MACLzB,KAAK,CAAC8B,GAAG,EAAE;MACX,OAAO9B,KAAK,CAACe,MAAM,GAAG,CAAC,IAAIf,KAAK,CAACA,KAAK,CAACe,MAAM,GAAG,CAAC,CAAE,CAACY,KAAK,KAAKC,CAAC,EAAE;QAC/DA,CAAC,GAAG5B,KAAK,CAACA,KAAK,CAACe,MAAM,GAAG,CAAC,CAAC;QAC3Bf,KAAK,CAAC8B,GAAG,EAAE;MACb;IACF;EACF;EAEA;;;EAGA,IAAIC,OAAOA,CAAA;IACT,MAAM/B,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,IAAIA,KAAK,CAACe,MAAM,KAAK,CAAC,EAAE;MACtB,OAAO,KAAK;IACd;IACA,IAAIf,KAAK,CAACA,KAAK,CAACe,MAAM,GAAG,CAAC,CAAE,CAACY,KAAK,IAAI,IAAI,EAAE;MAC1C,OAAO,IAAI;IACb;IACA,KAAK,IAAID,CAAC,GAAG1B,KAAK,CAACe,MAAM,GAAG,CAAC,EAAEW,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;MACzC,IAAI1B,KAAK,CAAC0B,CAAC,GAAG,CAAC,CAAE,CAACD,IAAI,KAAKzB,KAAK,CAAC0B,CAAC,CAAC,EAAE;QACnC,OAAO,IAAI;MACb;IACF;IACA,OAAO,KAAK;EACd;EAEA;;;EAGAhB,QAAQA,CAAA;IACN,MAAMV,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,IAAIA,KAAK,CAACe,MAAM,KAAK,CAAC,EAAE;MACtB;IACF;IACA,IAAIa,CAAC,GAAgC5B,KAAK,CAACA,KAAK,CAACe,MAAM,GAAG,CAAC,CAAC;IAC5D,IAAIa,CAAC,IAAI,IAAI,IAAIA,CAAC,CAACH,IAAI,IAAI,IAAI,EAAE;MAC/BG,CAAC,GAAGA,CAAC,CAACH,IAAI;MACV,OAAOG,CAAC,IAAI,IAAI,EAAE;QAChB5B,KAAK,CAAC6B,IAAI,CAACD,CAAC,CAAC;QACbA,CAAC,GAAGA,CAAC,CAACD,KAAK;MACb;IACF,CAAC,MAAM;MACL3B,KAAK,CAAC8B,GAAG,EAAE;MACX,OAAO9B,KAAK,CAACe,MAAM,GAAG,CAAC,IAAIf,KAAK,CAACA,KAAK,CAACe,MAAM,GAAG,CAAC,CAAE,CAACU,IAAI,KAAKG,CAAC,EAAE;QAC9DA,CAAC,GAAG5B,KAAK,CAACA,KAAK,CAACe,MAAM,GAAG,CAAC,CAAC;QAC3Bf,KAAK,CAAC8B,GAAG,EAAE;MACb;IACF;EACF;EAEA;;;EAGA,IAAIE,OAAOA,CAAA;IACT,MAAMhC,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,IAAIA,KAAK,CAACe,MAAM,KAAK,CAAC,EAAE;MACtB,OAAO,KAAK;IACd;IACA,IAAIf,KAAK,CAACA,KAAK,CAACe,MAAM,GAAG,CAAC,CAAE,CAACU,IAAI,IAAI,IAAI,EAAE;MACzC,OAAO,IAAI;IACb;IACA,KAAK,IAAIC,CAAC,GAAG1B,KAAK,CAACe,MAAM,GAAG,CAAC,EAAEW,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;MACzC,IAAI1B,KAAK,CAAC0B,CAAC,GAAG,CAAC,CAAE,CAACC,KAAK,KAAK3B,KAAK,CAAC0B,CAAC,CAAC,EAAE;QACpC,OAAO,IAAI;MACb;IACF;IACA,OAAO,KAAK;EACd", "ignoreList": []}