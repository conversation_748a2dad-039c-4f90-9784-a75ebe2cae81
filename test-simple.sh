#!/bin/bash

echo "🧪 Simple KiotViet MCP Test"
echo "=========================="

# Step 1: Get session ID
echo "Step 1: Getting session ID..."
curl -s -N http://localhost:3002/sse | head -n 3 > sse_output.txt &
CURL_PID=$!
sleep 3
kill $CURL_PID 2>/dev/null

if [ ! -s sse_output.txt ]; then
    echo "❌ No SSE response. Is server running on port 3002?"
    exit 1
fi

echo "SSE Response:"
cat sse_output.txt

SESSION_ID=$(grep "sessionId=" sse_output.txt | sed 's/.*sessionId=\([^&]*\).*/\1/')
echo "Session ID: $SESSION_ID"

if [ -z "$SESSION_ID" ]; then
    echo "❌ Could not extract session ID"
    exit 1
fi

# Step 2: Test tool call
echo ""
echo "Step 2: Testing kiotviet_get_categories..."
RESPONSE=$(curl -s -X POST "http://localhost:3002/messages?sessionId=$SESSION_ID" \
    -H "Content-Type: application/json" \
    -d '{
        "jsonrpc": "2.0",
        "id": 1,
        "method": "tools/call",
        "params": {
            "name": "kiotviet_get_categories",
            "arguments": {
                "orderDirection": "Desc",
                "pageSize": 5
            }
        }
    }')

echo "Response: $RESPONSE"

# Cleanup
rm -f sse_output.txt

echo ""
echo "✅ Test completed!"
echo ""
echo "💡 To see the actual response, you need to listen to the SSE stream:"
echo "curl -s -N http://localhost:3002/sse"
