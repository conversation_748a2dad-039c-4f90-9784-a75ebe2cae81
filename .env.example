# Application Configuration
NODE_ENV=development
PORT=3001
LOG_LEVEL=info

# MCP Server Configuration
MCP_SERVER_NAME=KiotViet MCP Server
MCP_SERVER_VERSION=1.0.0

# Health Check Configuration
ENABLE_HEALTH_CHECK=true
HEALTH_CHECK_PORT=3002

# KiotViet API Configuration
KIOTVIET_AUTH_URL=https://id.kiotviet.vn
KIOTVIET_API_URL=https://public.kiotapi.com
KIOTVIET_RETAILER=your_retailer_name
KIOTVIET_CLIENT_ID=your_client_id
KIOTVIET_CLIENT_SECRET=your_client_secret

# API Configuration
KIOTVIET_TIMEOUT=30000
KIOTVIET_RETRIES=3
KIOTVIET_RATE_LIMIT_REQUESTS=100
KIOTVIET_RATE_LIMIT_WINDOW=60000

# Cache Configuration
ENABLE_CACHE=true
CACHE_TTL=300000
