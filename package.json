{"name": "kiotviet-mcp-server", "module": "src/index.ts", "type": "module", "version": "1.0.0", "description": "KiotViet MCP Server - Production-ready MCP server for KiotViet retail API integration", "private": true, "scripts": {"start": "bun run src/index.ts", "start:http": "bun run src/server/http-server.ts", "dev": "bun --watch src/index.ts", "dev:http": "bun --watch src/server/http-server.ts", "build": "bun build src/index.ts --outdir dist --target node", "build:http": "bun build src/server/http-server.ts --outdir dist --target node", "validate": "bun run scripts/validate-setup.ts", "test-kiotviet": "./scripts/test-kiotviet-connection.sh"}, "devDependencies": {"@types/bun": "latest", "@types/cors": "^2.8.17", "@types/node": "^20.11.0"}, "peerDependencies": {"typescript": "^5.8.2", "@valibot/to-json-schema": "^1.0.0", "effect": "^3.14.4"}, "dependencies": {"fastmcp": "^1.21.0", "cors": "^2.8.5", "zod": "^3.24.2"}}