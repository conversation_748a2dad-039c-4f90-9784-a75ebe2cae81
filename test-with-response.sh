#!/bin/bash

echo "🧪 KiotViet MCP Test with Response"
echo "================================="

# Start SSE listener in background
echo "🔌 Starting SSE listener..."
curl -s -N http://localhost:3002/sse > sse_responses.txt &
SSE_PID=$!

# Wait for connection
sleep 2

# Get session ID
SESSION_ID=$(grep "sessionId=" sse_responses.txt | head -n 1 | sed 's/.*sessionId=\([^&]*\).*/\1/')

if [ -z "$SESSION_ID" ]; then
    echo "❌ Could not get session ID"
    kill $SSE_PID 2>/dev/null
    exit 1
fi

echo "🆔 Session ID: $SESSION_ID"

# Send tool call
echo "📤 Sending kiotviet_get_categories request..."
curl -s -X POST "http://localhost:3002/messages?sessionId=$SESSION_ID" \
    -H "Content-Type: application/json" \
    -d '{
        "jsonrpc": "2.0",
        "id": 1,
        "method": "tools/call",
        "params": {
            "name": "kiotviet_get_categories",
            "arguments": {
                "orderDirection": "Desc",
                "pageSize": 5
            }
        }
    }' > /dev/null

echo "⏳ Waiting for response..."
sleep 5

# Show responses
echo "📥 SSE Responses:"
echo "================"
cat sse_responses.txt

# Cleanup
kill $SSE_PID 2>/dev/null
rm -f sse_responses.txt

echo ""
echo "✅ Test completed!"
